.profile-dropdown-container {
  position: relative;
  display: inline-block;
}

.profile-icon-container {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.profile-letter-avatar {
  width: 35px;
  height: 35px;
  background-color: #ff4d00;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
  border-radius: 50%;
  border: 2px solid #ff4d00;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dropdown-menu {
  position: absolute;
  top: 45px;
  right: 0;
  width: 220px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-header {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.user-name {
  font-weight: 600;
  margin: 0 0 5px 0;
  color: #333;
}

.user-email {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
  word-break: break-word;
  overflow-wrap: break-word;
}

.dropdown-items {
  padding: 8px 0;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  text-decoration: none;
  color: #333;
  transition: background-color 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item img {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}

.logout {
  cursor: pointer;
  color: #ff4d00;
}

.logout:hover {
  background-color: #fff0eb;
}

/* Responsive styles to match navbar */
@media (max-width:1050px){
  .profile-letter-avatar {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
}

@media (max-width:900px){
  .profile-letter-avatar {
    width: 30px;
    height: 30px;
    font-size: 15px;
  }
}
