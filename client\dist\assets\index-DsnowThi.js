var e=Object.defineProperty,s=Object.defineProperties,t=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertyNames,r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,o=(s,t,a)=>t in s?e(s,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):s[t]=a,l=(e,s)=>{for(var t in s||(s={}))i.call(s,t)&&o(e,t,s[t]);if(r)for(var t of r(s))n.call(s,t)&&o(e,t,s[t]);return e},c=(e,a)=>s(e,t(a)),d=(e,s)=>{var t={};for(var a in e)i.call(e,a)&&s.indexOf(a)<0&&(t[a]=e[a]);if(null!=e&&r)for(var a of r(e))s.indexOf(a)<0&&n.call(e,a)&&(t[a]=e[a]);return t},h=(e,s,t)=>new Promise((a,r)=>{var i=e=>{try{o(t.next(e))}catch(s){r(s)}},n=e=>{try{o(t.throw(e))}catch(s){r(s)}},o=e=>e.done?a(e.value):Promise.resolve(e.value).then(i,n);o((t=t.apply(e,s)).next())});import{r as m,a as u,b as p}from"./vendor-DD48japz.js";import{L as g,u as x,a as f,b as j,c as A,R as v,d as y,N,B as w}from"./router-BZbynDg4.js";import{a as b}from"./utils-DpZGLU7u.js";var C,k,S=(C={"assets/index-DsnowThi.js"(e){!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))s(e);new MutationObserver(e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&s(e)}).observe(document,{childList:!0,subtree:!0})}function s(e){if(e.ep)return;e.ep=!0;const s=function(e){const s={};return e.integrity&&(s.integrity=e.integrity),e.referrerPolicy&&(s.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?s.credentials="include":"anonymous"===e.crossOrigin?s.credentials="omit":s.credentials="same-origin",s}(e);fetch(e.href,s)}}();var s={exports:{}},t={},a=m,r=Symbol.for("react.element"),i=Symbol.for("react.fragment"),n=Object.prototype.hasOwnProperty,o=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,C={key:!0,ref:!0,__self:!0,__source:!0};function k(e,s,t){var a,i={},l=null,c=null;for(a in void 0!==t&&(l=""+t),void 0!==s.key&&(l=""+s.key),void 0!==s.ref&&(c=s.ref),s)n.call(s,a)&&!C.hasOwnProperty(a)&&(i[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===i[a]&&(i[a]=s[a]);return{$$typeof:r,type:e,key:l,ref:c,props:i,_owner:o.current}}t.Fragment=i,t.jsx=k,t.jsxs=k,s.exports=t;var S,E=s.exports,I=u;S=I.createRoot,I.hydrateRoot;const R={logo:"/assets/logo-C537Eh51.png",basket_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAeCAYAAADQBxWhAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAJmSURBVHgBvVbBbtNAEH2zdaQcwx8EcUflD9xbmkuD1CBupF8APaPWTRFnwheQHCEghVODONT9gjoXLlxy7I1wAqnxDrN2XBzJXtdQ50lOdj3reTs7b3aXUIDWU6+pQr5kouHZh/5h3rh293jAwN4S9PjruB/YfCoUgEK8E2cNMHds45iwJ39NB/oNCmAlbe17rrhzI6caJ7axYh+upum29l+6+FdSRXi2as6nn05HtrH1Jb0lYBHRkvKsfvMMJpcM7pl2UZQGk0l/oRmDuGePNpdUhUhmWxhlgttGm0laNsoEt42W0h234zVqNTRrBI9XahXyA5QAsSidaKVg9pl1//fSCXyZ0BqpUSmRWc5YqdUgnsD042ufYkI+x4bAHO6oOMLNwQhMVbukmbTbhdtgBWiopK42iIUjP3N5trOsUi5DSYKPsmB2CdTLNnHgaFBA4EzS6fhVqRpNYbTbPe5lkppICUrOvjDzy/aToxOttY+SULTV4TyjrJwjBTsjyrYzk0e0Vbqk2GJT4EDVl7Ce8neNX9dqFsUoV41LzhFTAs08UORMcgeQ7sjSvIAdwdn49JFRr1w1RKHMdlJSoy9jL3dV2l3vp0zcSirVEH0fk5q8gmzj4bD2Wt2jzxaHz1EEHZdfRKpEwWxNP4zqOjKtDv4DoaJZ5Cp5IXn9Ed36qsNC8nnPNG72XpZNAhXC7ERJW6Ve+qgUf7fT1ClDF6gQIdGNCNcku9v1zis6X+eSz/tJZ+08lRkc3vVRZ/zxNe2k322lO9+/XVw9eOi+lwQ3TN3KB3XET3ki4Ep0MsRSHUwn/Xna/gekkPtdneji9AAAAABJRU5ErkJggg==",search_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAbCAYAAACN1PRVAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKxSURBVHgBvVbNctJQFD7nUpjqijcwC8eF44K+QIsrKZtmnNpxCU8gPkANoc7oTnwCWDrSccLG0nEh9QXKQle6iG/ATgWS4zk34JCQhp8JfjNwT25y73fPPb8ICSiaVv52zq8RqQNAMoDAkHkEGBLQAGDHobHf7Tm2CysA4yZLTy0DPWgBUBFW2gTb/hjsZaQqOnF4YtWUR9dCJBr4RE0kMNHLGRedBsqPN9+TOdauLWt4rGCWrh8dn9Yg8VBzKJ+c1onQCt6Q82ukqn3HHiZtILcAnm/xASp6GZL98f1ZPZFMNAKiNyL7ALXLTuMtrIHS8QuLVa7r9UTPL8/PmrFkcjq5OgLIE0G9d96wYQMcyjUiyoGHNMa9qA21zZQHliZiG2xKJLhgbRDRYTGPWb8Vfa9EKzGwfhqrjYlmyI2gKo7Fl1aU0AmRoaeORNBarRgvSXDYoQixLfLuDj0LkbE5TU1GWv104MGVDOyZxRAZAhVE8BF/QkqgLAwCCY0QmTiGCJ869gBSQu/dP3MYITL4j1Acaa4I5ccv70BKKD+xClNxGCYjdEXw1KgAKYH84PqIKGQaxRP96SdFSAmoIAgnDHu42p2gzoEKsRINwk0wnyRwjN0QmaOzOvZZzt/aWUwx60JSn4xxSUJ7I2WmKQbRXFaTkqBLlNQ22Ssm9WXk78fXq+Hd+/t/mKzEibR078E+fv/2pQ9rIChR8EofnuB170OjG/0mVDzna9KqpT7oU6DFjmbOTbtcYh5G1y70IFKTWDtrllmEdOJ7XeVlBrPFJdMy/IxXyGDmgPNfRb4NWghosidWpo3RAuGNDc98qV8GHT4TVZWNdbPk0+c4QkzaJOiy6IgrgqmQCjNteXSZwRUv/s2hE+**********************************/gVQtGFmKj9awgAAAABJRU5ErkJggg==",add_icon_green:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAiCAYAAADVhWD8AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAK/SURBVHgBzZhNbxJBGMf/M922JGrEeFF7WU2aWKQEEj0aqQevtBfP9QNo6cnoRTh48FTrFyhnL+XqSTjXBNryltjonoxHFGuksIzP8NZSdsvwtvBLlp2dGXb/eZ6ZZ54Zhj5IiZRbg8vPIEIAozt0AaG32hmYIQADEOkakPQxTxx9wFQ65UVeNyE2qLhOlxvqFElgnAPRJbZk9Op8oZiGJWYjDHwDQ0KiYr1E2Yo5FPkgh9ghs+sYEfQxcmNtzcu8aat2blWZEbkwjYvPoxQiabyPpzIiE4aKmEORjdBtC2OFb1kJ6nDTgThY5dB24RAC5soyW07gvBg5Y2pjcE0POcUKTm4HWKAon9puorjwxlkhEuaew/xO+0n+NOPId0yIlrvqlpFWwQRhmKl/nw1rlXffPqBwfIS7lxbx8s5zDEoF5WuaCTNoE26UkEL2flMMY0oriy2zcIU4megRpoMgp1XXj6lA+KV/dEwFQtfQR0pQOP6KUvVPR13ruVQtYe9Xqus/D64GoAZzM1oUhWJvPPnyFD/KP1W7Y2H+Bj7d/6jcX7qpiKlAFKVlpG2VBrGVVV7kXqPw96jujreLr7rab5F1FElrlIGlVWeU1YuvaJcvbFdHpGlqm0lMBTzBK6j0lcGPiwr+JXkzl0hgoogY6TC0ehFmlJaFIAbg8fWHWHDdrE/jQaEEKyrv7dUtK3K7FHBW4Tgi5mX3nslSe7k+QVlWOB1zjJZVOsTIsUPuWoOD1FDdlGMF58VIZOpnorYJB6B9WcTHfPHOOgv2aU8zQ3sbjAmyQNjDPNvoEmgDbbIoKnO5h9IxOgwSsk5CLAOtbb4p98OUl67I0Y7R8J7eF7ATIlFKXOk0Qp/FHGXwTE79vo5E6IqRiO2zA3UoMWfJikKIThKCVGweFp26sXHKIA+LkCaTx8so77d2iyr8B6Fv/i4/597HAAAAAElFTkSuQmCC",add_icon_white:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAACxEAAAsRAX9kX5EAAAYNSURBVGhD1Vo9TyRHEJ2Z5RYjIV2GEYGxBMYSgS8gP0iwiJwAoSNfRkCwASGSTUJqzA9ADh2tnQBHgqUNEewJIWFA3AkCAiSvEOJLsOv3aqpaveO1fMcO7GxJj/7Yme56XVX9NQS1Wi0MIPPz8xESgmXL5xL5Dg8vRkdHJSWGh4fzms+PjIxImoTV81l9npD3CbY3PT1t/Ujq6WUIWaf11NUgEpKQkpIXvIcdAXakyhgBU8QU62wAqfcUb1Tv2tL2rQ/27SMyHT0ilNAsYhASimQjRsIgihmBYrH4+eXl5Zvb29ulh4eHYrVafQ/8jfadsA6/bd7c3CxVKpXpvb29Ib4/ODhoxIyUQQYQEB3UWjmPhE8mtgZSIaAP+USsMSPi3ILKQ6nC/f39Zqzqpwvfvbi4eIP26sgkLFNHxtORMCPUEyE8P5XGvEZl9NbW1now8j8lR7wZgaXes030Ye4mKfvW/usIeWQcESnYD0rCXnQEFJ3n5+fj7FT7T13YNl2UfRFKyvSoIwP4ZFyGqT1QR4KNqRWWtL8nF/T1M12X/SsaWcYRMQLCSNMQLwRbW1tWDhCUXw4NDf2Wy+W+Yfm5BK774fj4eBzu/AHFWlwbp5hBg56enhoGOa6nS3kxYTBrdHJmeUpX+j9h34eHh1+piwm4DOgaZusOjRG7k5HxX2g1CRPqQF1ML2/yERcT3X1r2AOcmeifWSBhgmm67MVMHRGFMw8r3QL3nIH9scIJgIPsWcQnE1sDELPxQU5/+m7mhNO/6Qo0JGIbv84suVRSqBuXAuhpRIxMTAIQllxd9Z3MyvX1NXcAFgaOiCMBZNoaJtwaNQh8YSNEshwbSbm6uiqo3kIkgnm4mHB1DLu6ur5H2haSz+e/QxJCf9l9yIoICbHg9EdR9FrLmRdsl16XSqWXWhQyEhs86KjVUpODg4Pa7OysgPm0RXfJstKbWwVwq9StgY6CcrksYD5t6ejoeMWUG9yIO12tfNadbRoCnb9AjDAbRmBD/6qFYdjPmnYS6PwKsS15F+xtSuQltlQS6PzDYOe5/YYVjxXGAM4NWoqF5eXlZcnPzMwE6FTyJr29vYJmBGQ+QyKXDty2R5hVmiKyuroaLC4uaunjZG5uLpiYmNDS48SI0LVCjBxns4r80n4isy7P5y+wCQt3d3f/aiZO6FrJKXZnZ8dZaWFhIRgYGJC8SXd3t+CxwjM9FsavwaHKsuyzcAL7nWZJU7a3t2tjY2MC5tMWXu6p/rIgUkLsenlT0VYCnd/pOhJw0xgA3BqXpaaNBGenTbeOIFNjYX19/Q+paSM5PT19p1lZR7ifZxrC59a5q+QPacjZ2VmwsrIi+cnJyX+tI80I3OpPbFF4fg8Q7DJz2Vk9z5t1jaPMC2baH6gz9QfcUZeQu6w0b9ifSngcp76AI2KzFqVaKBQqCKBftJxZubu7+1WzdKs4o5dzZCV3WhsbG5m6YUyKWYPhoCFh98DuOsiQ5yWYvpc5we5BToUK+9SQ4/U8iRgrIUKm/M6n72ZGqBP0Y2zYoIvugNw02r2vI0Iw8HlxrG20XOhSpVKp0Q0j9c9x/RA2FJhJgl9PjcHJyUl/X1/f2yiKWnro4uZwf3//26mpKcYHPzxx3ZC1AzHuPvZQ+Zy5mMJZ5ujoqOUfeqC4+zaiutVZAxADcPSFDOATMbTsgw9duwEJiWedbal3DFZ4lT4ZR4jBT/98zgmAfVlMsH+dnYQIQD0N8f8G8I/3zdp9glPYy+4DEKe/p7QO28YebVz7EygJ0Un1c/84oLpLTMuGERAy9qDCJ+P2ZFw4ebWfJiFujbCr+NH79lFnBRtgTf3BN/3jP0x9yyhkFLwGHRkDLcSTmurzycJ3uVm1zwTsy1yJ8OJBkCBgqYgjAghTfdjcLEnIrOTI0EIMSt4f07epXNJaHHHW4bciRn6JA2DKJ93I64dwJADfEvKfQgTywkEyfmUDyxDWsBHx4RShUqqY/AsGSTK1Oi075Vn2lU8sA/5g+v8V5IMiafIHg2MO/BchgygBODJMk0obVGmBP/pGQlPrz9fDYEI9IUH4D73WUcZJ0wQDAAAAAElFTkSuQmCC",remove_icon_red:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAiCAYAAADVhWD8AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAIKSURBVHgBzZjJTcNAFIb/N3BFMldOpgFIKiCpAOgARAGECggVQAqIkg5IB5AKsjSATzlHyjke/hnbyiLHSxbbn5TF9tjzaZ7teW8EOdCjkcOfGpS6h0gNWrvcdteaeNzn8dgYvj+Uen2AHEiWRpRwKfDKv0/8OMjOnGIDLJcfFPPSGkuKhEOJNv++4lBE+mlSkiDSoEgPm2E4FI/he6TQOO6gQrxIiyI/RxaBvZ5SI3v9LDJ6MmnzhE+cEl4/TmgjTGzwwIbfKArfbzJkv9iWCZ+YU4QmiTmFrik0NxurMJ2dvRcsYnDYby/asCMTjsofyiIMVzAywaiUh4jtX0oflQjfvzQj00AV4HynGKI7VAGtG4pfNVQBZgEmTC6qgXuOfCkB8PICzGbJba6ugG4XOXHOkRcjkiazJ0bGvIqzj06zCSwWyW0uLrAHc+EsPYJJJctnbJ6mMaoAPYzMEFVAxM5NuTL4k8FqQtlcglYoE637JlEPZm1m7SgTrW3/Vsamfqa+KUekH5Uvq0xvuXxG8M4pEi8alQ0Ze++wpkGR+P7belG3UarYcLEBiqG9XYvHVpRhEXfK2qklt7ed7Z1J5W0trKFcHA+zQvEkNzexL1q16yxbDzNrN3c7jsMXr1ffJWL7RAZs0m4yeJEH5F0SAfqU6By8JBIrNp3em3w1dbEoWJeZRNViFv4BR9XKFZuL/aIAAAAASUVORK5CYII=",app_store:"/assets/app_store-C8O_cY6s.png",play_store:"/assets/play_store-B2tFv0Hy.png",linkedin_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAALHSURBVHgB1ZnhceIwEIUfmfw/XwWn64AO4g6gg9ABdABXwlWQXAXQAaYC6ABfBSEVKLv4CQQhYMk2dr6ZNwZbIz/ZsrQr9VABa20ih1T0JDKiviihlJ0o53EjWomyXq+3QyQ9RCBGUzlMaTIXLXhUU+/OEBukZX6gaJiTlv8n5RZoEjEwFG1FS9GYhkLrSEQj0Zp1PaNupFJDk6on1ITWRdNq3qAOpKIJKx2jIbx7TFAFqWDGigwahm9R7zVDDDS7jumnsdD0Oth0G2a9eydBpr3+ZNASNH27T3v9yKBlSnnhq2hsNAiFb3v51UUdzLfoGBz7h5cubEMmBb6yueilyS6kYcCnB8kpd40A2EDHHA3Cp5z6J+ahc7o9JaixoTBuWbo/CW8aNOaykY4pGsQb5hLXHZaRFemHOsAdYLcYPKIIvjNEIPHsa5lyciMX2G8qBO8aO6cPKLKEDIGwxY4Xnht55/QV9vmFax/Xt/hmY4Mb4L/IqGFt+TvqR+vV0cOcnZ+efPHl0Wym/4BjmlM3Lj26RMxHql0p2T/hKknhDV5FP0W/UTwhh0Eg9Lg33CR/9EaiHIV5h0EkanhnG4p7adRR6Tuhx93eMIo0vOsYUa6G918fus/hCeeo0KfuiE5wGzWsy0dDdJ9UlKnhTGRsCwlnWexxDW/1qMOOnMjlj4aXfwPq2Vz4/Yavp/lr126hPWBxmC8Y1UdFbPeAuebg/OQ2co5vlIspEi8Mu/iU7bUVToaM1RbkaoRp/vpage+1kMKCk0P+1BKe2XKLOppU2vYXA8NiZs+0wZ2INutVMLX3X9CutmRg61rOL3ePehYi7XFlvNbJxc2wlEHdMI3f0vyzjd/2GtOk1hUUKcZuLOq8PkIRlGSUhqn7nc+zjUWVJgi/WN6giMFnUm6FQKIMe8bVjAbWKY7btganW7du2zYHG1YlS/8AQV35ND++wScAAAAASUVORK5CYII=",facebook_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAKhSURBVHgB1ZnvcZtAEMVXmnw3HfhKUAeig6gDuwO5AykdpISkgqgDoQpQB8IVWKlgs088wvk/3B0C/2Z2YASz94DV3t7eTCJQ1cwOudnSzJktzDIaOJtVPB7NDmbFbDY7SyAzCcCE5nbYUGRltuMRov42gvhAuOdG6gdrDPf/tvt2MiQmYGV2MtubrSmor4/M7N6spK87SY05dRQJW0oi4IuiId5JCszRA52uZSC8MR4kBnOwpSMnA8OviLG2EgLFliFxGgpFl71FjyHWGzvrJdqLJycjQdGfx7QXR05GppMWforBskFf+LX3711EMj/JxGDuX7114ZRqUjA/Cw70pC04X/R0dSkDXr1ITrmlJICx5wv1CXohfPjc/+FPqjmdzjWx4PX/WGYK0RQ51/PVcNK6XrilBY2hbZrLvkld7kXVqB4vBe3M70EigTYTW9npci518V3IMKR4CQ2onXMIxj+3kOnzKFgMcLLonW4amHY6IREoZ765tMucoTlKHAivDIKzRH+4LgMGQ42XLBEL3tw9z5ElfnrXdjTwKCnQelZKUvcyznw2kgjm4ieEBF71jUwfZ1ZBMD5pcJa4IoiCMwRXUqufOpjgjhCMqXMl0yc3KyC4MHM6woKzK9r28A5z5rfKLH3LKB2IABRSlxgGP2TaYYF15q9nv7DezGVi6FtLJF5Y6Xsr1BHRjzqcWi9v4hpyCdF6mV9+dMPXaqTwxqZVNVqa88R2a+qgaNHxm4H9CidPtJMrESzWc7DR6ze040pSTdXO7zZGmkaktp3xpJMLJ4U9zUlqlB1Oir/T8G2vNUXCV6+SIHRj8bvU6zgMVtBQpl4KqRcbizAsEG55v5O62NqGdIWCBHvCIQaFdS7ttq2T51u3zbZtJXywmFX6P+g8K/PIslj4AAAAAElFTkSuQmCC",twitter_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAMFSURBVHgB1VmBdeIwDBVdoGxwHoENyE0AG8AGdAPYoHcTXG8C2CB0Au4mSDaATqCTyPfFUEoiO4H2v6eXljjSty3LsjygBDDzUB6ZyFjEiYxEhhDFQaTE84/Iq8h2MBgcKBIDioAQzeSxBMlSZIOnknrzhNAhbfNIVce8aPvf0m5DfUIITEUKkVxkAUJWHUORucgOumbUNUSpA0mVMXUE1QXSSt5RFxBFT1C6oJ4Q2HiiFIiCFRQ56hmYRbW1ohiA7C7GT2MB0jsz6XuQDWwPTaQDf3J0J4B0s08HfuTozmjFBVPRWzSwArOdf/RSg3lBnwyI/dNLL4ouN4WuoGnAu4HElrujTwqMchb+sO5lT6/1z2HUb8Oz4J1rWuTIW3L/j4YQtsZcdNK1aLfiy1j7TrTQ4cPc0LtDTgYEnbwadqD7GvaIBI2zi85NHqhKvrdkg58NJ5JfMTihZj0aRl+pGZo7Z565OTpgdELkJwuD/o9K0wi7lvZ0ttYP6OUb2fHj7P+MqtEu4J9LqmfiIxzk1FFSO+hpZuRHyrrg1IdnIJaC1msHNvf6B5MRXC+6VKyNdvmBIoBD5pbSYT+EcoRL4DufTaXAGewdXUJHWEfrkYzAYvku8gIdVmwMC07hREolXK2+OGicnVNzNLiEFdmgNg5KuKSKvRkyQj+p6rAVL/LtX7JhfLTFEVtzCPjWs8Gfi8g1k4tMThOLBKDj+xZkHRnB5wka2JuPRlxFigU3b8GMNo4iwFV6ug5/yNi264xakmSMetI5kascenL+Y8FnyUtL4s9cJ+de1MAvrmpnqa72/oiEF0mLry/wtQonRiqtINchuErud9cafK1CChr6UtXN62oBB0+23WKVhku+fzFwaf3Qk3Z0I0STDRQs+fYF7TiygaJuyvntbHRTiOS6Mm7eXBr0Zlxf9jjqGowKJ8jPOP7aaxHsjlPL97EXiz5xV2NbiBZDjjefZxeLKnpA+Ib2jqocfCXt2hRQ0gkHxJWMJtYZ1de2jk6vbv21bUnoWMrV7T8ArU1k5aRcjgAAAABJRU5ErkJggg==",cross_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAH9SURBVHgBnZS7TgJREIb3Av3a2bFcQiyh0w4IJFjJI+ATiE8gVFpiaaWWVkJlDIRLJR3YaUJgS0voNNz8/8MegpsFFic52dlzZr6dMzszumJLJpO5CIfDP4PB4EvZQxKJhBGJRK6j0ehnv98faTYstlgsylhN6l5h2WzW9Pv9XaiFyWRyxT0BrNVqPTzKWAahNPQCm81mTdibWBb00gpIqdfrl3g8EkrDbVDeQsLw2oMeb7Va1h8gZTqdFmhAw01QOz0SVoFPErCRPFedDkyyrutdVVXF19cdUqlUXtO0e+Goqg9I1bnTX3Nu0BnRJZkXvMZ8Pt8z99PpdEHCIEU3mGuEa5GaiLQpIyVcwpDv0ia/jUAJRYQsC0MYq2oBkd1u89G2HQJWlDAK0nCg7BBdcY+M1f8CNYfFH3LDba5QKDRGN3U8A+1SIeyYPwY/4hQ5ezJNc4wrZ2kSDAat4XD4vhMoqx/qkV39yUaj8cEzADqITlwA4Bz0NiK1NgJdWikpq18KAG0JZTowTF6dw0QAWf3z+fwN6qGybKUTwFynjg0NMiVMAaZMlVNGnmuylaAbrH5nK7kJcprHo+LWoho2z5RlaZRZ/btgUvBhdsqq71kZAsiqR2Rxe9p4Fn6Yt2G+CUXNinkocrjvlJZiWdZ3IBCoIiAD5XX3X85W+QXZyj6p6rhLawAAAABJRU5ErkJggg==",profile_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAgCAYAAAAMq2gFAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAI2SURBVHgB7Zc9cxJRFIbfcxFHrLa0ZMaPIuNkKK3C2gUqCslolfyDUNro7pJxHCvxH5hSjZlNo1C5pLJzG9OYGfkJWOkksMdzCTBM5C5chC7PDJ/34+XsPee8C2EONh97eephF4orYOSHX8cEipNzBM0w6Mzag2ZNKG15NTC/Tt2EOPj0fs9Pm5NJGyxvPfMlgpeYCbn37m/Qj+/HkXGGaWBwufr8ExYw9x82D15E08aUaZHqw4MtfG3buF/KqgosIWVeYxRiwIE9jrWQHF4X9nSthRJQCEsYHFoLyap92HKuAtOQsY5OT9qdO2tFKUa4mANm+M3D+pG10FCsPY9YAtRaB/VXaXNShcZi6+6+/GSHLur7ln6SrOww81v01JPWYb2FK1aFsam6Fc/JZdU2ISlIfRRkpjPhRaPVHTk7eaguswrRT9omb/pHaPOR50qWSUNlFwvBEXMSXO7iNBnBzSz7kk27WAZEjd9nCKIwGLSlcWfIZfnL0kQ0zDW9pw5gLFSuPm/ISwHLp5C7fuFrVK56+rC/YYVo51WSSfZOagmR8hRo0eyykiqoBZ3UFkeSgSKsnlgiSuwNzhJO0MicnhzHNgZnLaIN8WP9zcCPtOfcXdv4RUQP5OMNLAF9cyOG+HRkiGPjk8i+3l533w0N7r+KVwwx4p4qTRri1O5dqgZFRm9HBHdgKSBH4H/+sNe+PJb6b0Lff0vrd+VtUUcpk/OjctCXZmDnCcXIcPTnjI5GDXQafwHQRtx8CeJHgwAAAABJRU5ErkJggg==",logout_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAmklEQVR4nO2WzQ2FIBCER0uwpwdejRUAFViBFRjrsiS9zIt5z6sXCOMPk3Bk+bKz7C5QdEUxWKY49weA6D4KAFNY4O0mtMCsdG2nA3ARjyep4l8WGobPoMnAONb0dvnHmbMD7KI3/V6IBwSBCjkBoiB4AhDXns10f4AYC6RFiPd9w6BsRE7ciikfRkE+jm1ZyfiMtZwqgCJk1BcpfwInKVzXUQAAAABJRU5ErkJggg==",bag_icon:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAACkUlEQVR4nLWXPWgUURDHVxKsDCGYYK0mwaS1SEDhwOzO7HHhrBYJ4eT2zXIQDWlsbGKiiCkNKawVFdFGMIGIgp2FGA87mxAFtbEQ7JSoI+8j6yWyt+/i2wcDt+/+O/N7u/PezHrePgaTP8ICl1jgqrElbsCJ/fjqaLDnHdCB4RcT/GDC1yxwQ/+GnyzwutQUByDgBhMyC7jLcTiQzsfhABPe0//h1WKCx+GoWeX9TA3BAxa4zUl5uJjVC/jOtYnDmZrGZL96HQKuuQcgWGOCpoWuKRPTPYBQybaWr4OnMjndAxC8Z4G3LXQyD7b+L1gssxqWWeAzJnhuTL7bjy3XGYafVa4QPGqxK3yhdKiD1eIL4+SNefRNtcUIPqnrdiY1WitzYYMJ3+pzAx/bBRfVHib8zYTzu7NbOZ3Nh4dZpW1M9u8+P+QWrvbkAyTBUXOg1NO5uj+k5hKcyr8fp5S27g/9BcCaWcCgBQCcNADVdG6h1M0El3mm0pd7/0ylT2kXSt0tABXlMw7GLAAQNW1wynM0OAnHDUDZs36E5I+4AygPmxoybQNwUQOcOeIMIE1inMsXE84rcRQddAYQRV1mKy7aANxkgm+ugqd+BXxlwhUbgDvy2C0AYFP2DBZCXJUnoHsAfMUC122EL+WZXgDAuoSwEb5jgofOAWTLJmDTQghfWOCtAgBWZCLadL3bsrstAGBRbcUo6soWNfxeUzQuFQAwt7dK/ivaqYQEsXMAAdO6orbpmHmnEiZ41jlAHJS173A8W0QQ6KoVni4AYMyU+Uo7gHMGYNQ5AMGgAai1oYTzOlHC484B4oljpiIm2aK6bL3Uh+aH/O63QxO4pbZ4Xp/BMfos8Elu99up6S+rcG/AP/JIYRp2dTajAAAAAElFTkSuQmCC"},B=[{_id:"1",name:"Greek salad",image:"/assets/food_1-iEOlQHK4.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Salad"},{_id:"2",name:"Veg salad",image:"/assets/food_2-Bviin0XJ.png",price:18,description:"Food provides essential nutrients for overall health and well-being",category:"Salad"},{_id:"3",name:"Clover Salad",image:"/assets/food_3-DFGIQWuA.png",price:16,description:"Food provides essential nutrients for overall health and well-being",category:"Salad"},{_id:"4",name:"Chicken Salad",image:"/assets/food_4-DA8g0Kmx.png",price:24,description:"Food provides essential nutrients for overall health and well-being",category:"Salad"},{_id:"5",name:"Lasagna Rolls",image:"/assets/food_5-DCZk6AQV.png",price:14,description:"Food provides essential nutrients for overall health and well-being",category:"Rolls"},{_id:"6",name:"Peri Peri Rolls",image:"/assets/food_6-Cz8hlc5i.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Rolls"},{_id:"7",name:"Chicken Rolls",image:"/assets/food_7-xL5P3oOv.png",price:20,description:"Food provides essential nutrients for overall health and well-being",category:"Rolls"},{_id:"8",name:"Veg Rolls",image:"/assets/food_8-Cl7of1B4.png",price:15,description:"Food provides essential nutrients for overall health and well-being",category:"Rolls"},{_id:"9",name:"Ripple Ice Cream",image:"/assets/food_9-CQtsjFOM.png",price:14,description:"Food provides essential nutrients for overall health and well-being",category:"Deserts"},{_id:"10",name:"Fruit Ice Cream",image:"/assets/food_10-Cdj7aC_-.png",price:22,description:"Food provides essential nutrients for overall health and well-being",category:"Deserts"},{_id:"11",name:"Jar Ice Cream",image:"/assets/food_11-DfRPpaFP.png",price:10,description:"Food provides essential nutrients for overall health and well-being",category:"Deserts"},{_id:"12",name:"Vanilla Ice Cream",image:"/assets/food_12-TcaAZGUd.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Deserts"},{_id:"13",name:"Chicken Sandwich",image:"/assets/food_13-k_d-dzFx.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Sandwich"},{_id:"14",name:"Vegan Sandwich",image:"/assets/food_14-C9vU_xdJ.png",price:18,description:"Food provides essential nutrients for overall health and well-being",category:"Sandwich"},{_id:"15",name:"Grilled Sandwich",image:"/assets/food_15-CLR4VtEo.png",price:16,description:"Food provides essential nutrients for overall health and well-being",category:"Sandwich"},{_id:"16",name:"Bread Sandwich",image:"/assets/food_16-DqWMQk4h.png",price:24,description:"Food provides essential nutrients for overall health and well-being",category:"Sandwich"},{_id:"17",name:"Cup Cake",image:"/assets/food_17-B8PBV8Ml.png",price:14,description:"Food provides essential nutrients for overall health and well-being",category:"Cake"},{_id:"18",name:"Vegan Cake",image:"/assets/food_18-D9lmXWyv.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Cake"},{_id:"19",name:"Butterscotch Cake",image:"/assets/food_19-CFHillXF.png",price:20,description:"Food provides essential nutrients for overall health and well-being",category:"Cake"},{_id:"20",name:"Sliced Cake",image:"/assets/food_20-CTe0fZJ7.png",price:15,description:"Food provides essential nutrients for overall health and well-being",category:"Cake"},{_id:"21",name:"Garlic Mushroom ",image:"/assets/food_21-BrJc8UvX.png",price:14,description:"Food provides essential nutrients for overall health and well-being",category:"Pure Veg"},{_id:"22",name:"Fried Cauliflower",image:"/assets/food_22-BVMfdZ1K.png",price:22,description:"Food provides essential nutrients for overall health and well-being",category:"Pure Veg"},{_id:"23",name:"Mix Veg Pulao",image:"/assets/food_23-BxkRb3Vg.png",price:10,description:"Food provides essential nutrients for overall health and well-being",category:"Pure Veg"},{_id:"24",name:"Rice Zucchini",image:"/assets/food_24-D5JYPl2r.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Pure Veg"},{_id:"25",name:"Cheese Pasta",image:"/assets/food_25-BzfbEGNp.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Pasta"},{_id:"26",name:"Tomato Pasta",image:"/assets/food_26-ButgZpoJ.png",price:18,description:"Food provides essential nutrients for overall health and well-being",category:"Pasta"},{_id:"27",name:"Creamy Pasta",image:"/assets/food_27-Bd1jqUGP.png",price:16,description:"Food provides essential nutrients for overall health and well-being",category:"Pasta"},{_id:"28",name:"Chicken Pasta",image:"/assets/food_28-D2lOoPiC.png",price:24,description:"Food provides essential nutrients for overall health and well-being",category:"Pasta"},{_id:"29",name:"Buttter Noodles",image:"/assets/food_29-B8GW2frB.png",price:14,description:"Food provides essential nutrients for overall health and well-being",category:"Noodles"},{_id:"30",name:"Veg Noodles",image:"/assets/food_30-CfTld12c.png",price:12,description:"Food provides essential nutrients for overall health and well-being",category:"Noodles"},{_id:"31",name:"Somen Noodles",image:"/assets/food_31-BgWMzztU.png",price:20,description:"Food provides essential nutrients for overall health and well-being",category:"Noodles"},{_id:"32",name:"Cooked Noodles",image:"/assets/food_32-BKt-jRyi.png",price:15,description:"Food provides essential nutrients for overall health and well-being",category:"Noodles"}],T=new class{constructor(){this.cache=new Map,this.timers=new Map}set(e,s,t=3e5){this.timers.has(e)&&clearTimeout(this.timers.get(e)),this.cache.set(e,{value:s,timestamp:Date.now(),ttl:t});const a=setTimeout(()=>{this.delete(e)},t);this.timers.set(e,a)}get(e){const s=this.cache.get(e);return s?Date.now()-s.timestamp>s.ttl?(this.delete(e),null):s.value:null}has(e){return null!==this.get(e)}delete(e){this.cache.delete(e),this.timers.has(e)&&(clearTimeout(this.timers.get(e)),this.timers.delete(e))}clear(){this.timers.forEach(e=>clearTimeout(e)),this.timers.clear(),this.cache.clear()}size(){return this.cache.size}getStats(){const e=Date.now();let s=0,t=0;return this.cache.forEach(a=>{e-a.timestamp>a.ttl?s++:t++}),{total:this.cache.size,valid:t,expired:s}}},F=(s,t,...a)=>h(e,[s,t,...a],function*(e,s,t=3e5){const a=T.get(e);if(a)return a;try{const a=yield s();return T.set(e,a,t),a}catch(r){throw r}}),P=(s,t,...a)=>h(e,[s,t,...a],function*(e,s,t=3e5){if(!T.has(e))try{const a=yield s();T.set(e,a,t)}catch(a){}}),z=e=>{if("string"==typeof e)T.delete(e);else if(e instanceof RegExp){const s=[];T.cache.forEach((t,a)=>{e.test(a)&&s.push(a)}),s.forEach(e=>T.delete(e))}},L=new class{constructor(){this.metrics=new Map,this.observers=[],this.isEnabled="true"===localStorage.getItem("eatzone_debug")}startTiming(e){this.isEnabled&&this.metrics.set(e,{startTime:performance.now(),endTime:null,duration:null})}endTiming(e){if(!this.isEnabled)return;const s=this.metrics.get(e);s&&(s.endTime=performance.now(),s.duration=s.endTime-s.startTime,s.duration)}measure(e,s){return h(this,null,function*(){this.startTiming(e);try{const t=yield s();return this.endTiming(e),t}catch(t){throw this.endTiming(e),t}})}monitorImageLoad(e,s){if(!this.isEnabled)return;const t=performance.now(),a=()=>{const s=performance.now()-t;this.trackImageLoadTime(s),e.removeEventListener("load",a),e.removeEventListener("error",r)},r=()=>{performance.now(),e.removeEventListener("load",a),e.removeEventListener("error",r)};e.addEventListener("load",a),e.addEventListener("error",r)}trackImageLoadTime(e){this.imageStats||(this.imageStats={totalImages:0,totalLoadTime:0,fastLoads:0,slowLoads:0}),this.imageStats.totalImages++,this.imageStats.totalLoadTime+=e,e<1e3?this.imageStats.fastLoads++:e>3e3&&this.imageStats.slowLoads++,this.imageStats.totalImages%10==0&&(this.imageStats.totalLoadTime,this.imageStats.totalImages)}monitorApiCall(e,s){return h(this,null,function*(){if(!this.isEnabled)return s;performance.now();try{const e=yield s;return performance.now(),e}catch(e){throw performance.now(),e}})}monitorComponentRender(e){if(!this.isEnabled)return{start:()=>{},end:()=>{}};let s;return{start:()=>{s=performance.now()},end:()=>{s&&performance.now()}}}getCoreWebVitals(){if(this.isEnabled&&"PerformanceObserver"in window){const e=new PerformanceObserver(e=>{const s=e.getEntries();s[s.length-1]});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e);const s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{})});s.observe({entryTypes:["first-input"]}),this.observers.push(s);const t=new PerformanceObserver(e=>{const s=e.getEntries();let t=0;s.forEach(e=>{e.hadRecentInput||(t+=e.value)})});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}getSummary(){if(!this.isEnabled)return{};const e={totalMetrics:this.metrics.size,metrics:{},navigation:performance.getEntriesByType("navigation")[0],memory:performance.memory?{usedJSHeapSize:performance.memory.usedJSHeapSize,totalJSHeapSize:performance.memory.totalJSHeapSize,jsHeapSizeLimit:performance.memory.jsHeapSizeLimit}:null};return this.metrics.forEach((s,t)=>{e.metrics[t]=s}),e}clear(){this.metrics.clear(),this.observers.forEach(e=>e.disconnect()),this.observers=[]}setEnabled(e){this.isEnabled=e,e?localStorage.setItem("eatzone_debug","true"):localStorage.removeItem("eatzone_debug")}};"undefined"!=typeof window&&L.getCoreWebVitals();const U=6e5,O=9e5,Q=18e5,D=12e4,M=3e5,W=new class{constructor(){this.baseURL="https://eatzone.onrender.com",this.axios=b.create({baseURL:this.baseURL,timeout:1e4,headers:{"Content-Type":"application/json"}}),this.axios.interceptors.request.use(e=>{const s=localStorage.getItem("token");return s&&(e.headers.Authorization=`Bearer ${s}`),e}),this.axios.interceptors.response.use(e=>e,e=>Promise.reject(e))}getFoodList(){return h(this,null,function*(){return L.monitorApiCall("GET /api/food/list",F("food-list",()=>h(this,null,function*(){return(yield this.axios.get("/api/food/list")).data}),U))})}getRestaurants(){return h(this,null,function*(){return L.monitorApiCall("GET /api/restaurant/list",F("restaurants",()=>h(this,null,function*(){return(yield this.axios.get("/api/restaurant/list")).data}),O))})}getCategories(){return h(this,null,function*(){return F("categories",()=>h(this,null,function*(){return(yield this.axios.get("/api/category/list")).data}),Q)})}getUserCart(e){return h(this,null,function*(){return e?F(`user-cart-${e}`,()=>h(this,null,function*(){return(yield this.axios.get("/api/cart/get")).data}),D):{success:!1,message:"No token provided"}})}getUserOrders(e){return h(this,null,function*(){return e?F(`user-orders-${e}`,()=>h(this,null,function*(){return(yield this.axios.get("/api/order/userorders")).data}),M):{success:!1,message:"No token provided"}})}getHomePageData(){return h(this,null,function*(){try{const[e,s,t]=yield Promise.allSettled([this.getFoodList(),this.getRestaurants(),this.getCategories()]);return{food:"fulfilled"===e.status?e.value:{success:!1,error:e.reason},restaurants:"fulfilled"===s.status?s.value:{success:!1,error:s.reason},categories:"fulfilled"===t.status?t.value:{success:!1,error:t.reason}}}catch(e){throw e}})}preloadCriticalData(){return h(this,null,function*(){Promise.allSettled([P("food-list",()=>this.axios.get("/api/food/list").then(e=>e.data),U),P("restaurants",()=>this.axios.get("/api/restaurant/list").then(e=>e.data),O),P("categories",()=>this.axios.get("/api/category/list").then(e=>e.data),Q)]).then(()=>{}).catch(e=>{})})}updateCart(e,s){return h(this,null,function*(){if(!s)throw new Error("No token provided");try{const t=yield this.axios.post("/api/cart/update",{cartData:e});return z(`user-cart-${s}`),t.data}catch(t){throw t}})}placeOrder(e,s){return h(this,null,function*(){if(!s)throw new Error("No token provided");try{const t=yield this.axios.post("/api/order/place",e);return z(`user-cart-${s}`),z(`user-orders-${s}`),t.data}catch(t){throw t}})}clearAllCaches(){z(/.*/)}},J=m.createContext(null),Y=s=>{const t="https://eatzone.onrender.com",[a,r]=m.useState(()=>{const e=localStorage.getItem("token");return e||""}),[i,n]=m.useState(()=>{try{const e=localStorage.getItem("user");if(e)return JSON.parse(e)}catch(e){}return null}),[o,d]=m.useState({}),[u,p]=m.useState([]),[g,x]=m.useState(!1),[f,j]=m.useState(!1),A=m.useCallback(()=>h(e,null,function*(){try{x(!0);const e=yield W.getFoodList();e.success&&p(e.data)}catch(e){p([])}finally{x(!1)}}),[]);m.useEffect(()=>{A()},[A]);const v=m.useCallback(()=>h(e,null,function*(){if(!a)return null;try{j(!0);const e=yield b.get(`${t}/api/user/profile`,{headers:{token:a}});if(e.data.success){const s=e.data.data;return n(s),localStorage.setItem("user",JSON.stringify(s)),s}return null}catch(e){return null}finally{j(!1)}}),[a,t]),y=m.useCallback(()=>{r(""),n(null),d({}),localStorage.removeItem("token"),localStorage.removeItem("user"),localStorage.removeItem("cartItems")},[]),N=m.useCallback((e,s=null)=>{e?(r(e),localStorage.setItem("token",e),s&&(n(s),localStorage.setItem("user",JSON.stringify(s)))):y()},[y]);m.useEffect(()=>{!a||i||f||v()},[a,i,f,v]),m.useEffect(()=>{h(e,null,function*(){const e=localStorage.getItem("token"),s=localStorage.getItem("user");if(e&&!i&&!f)if(s)try{const e=JSON.parse(s);n(e)}catch(t){a&&v()}else a&&v()})},[a,i,f,v]);const w=m.useCallback(()=>h(e,null,function*(){if(a&&(null==i?void 0:i.id))try{const e=yield b.post(`${t}/api/cart/get`,{},{headers:{token:a}});e.data.success&&d(e.data.cartData||{})}catch(e){}else{const s=sessionStorage.getItem("guestCart");if(s)try{d(JSON.parse(s))}catch(e){}}}),[a,null==i?void 0:i.id,t]),C=m.useCallback(s=>h(e,null,function*(){if(a&&(null==i?void 0:i.id))try{yield b.post(`${t}/api/cart/add`,{cartData:s},{headers:{token:a}})}catch(e){}}),[a,null==i?void 0:i.id,t]);m.useEffect(()=>{w()},[w]),m.useEffect(()=>{const e=setTimeout(()=>{a&&(null==i?void 0:i.id)?C(o):sessionStorage.setItem("guestCart",JSON.stringify(o))},500);return()=>clearTimeout(e)},[o,a,null==i?void 0:i.id,C]);const k={token:a,setToken:r,setTokenAndUser:N,user:i,setUser:n,isUserLoading:f,fetchUserProfile:v,logout:y,cartItems:o,setCartItems:d,addToCart:e=>{d(s=>c(l({},s),{[e]:(s[e]||0)+1}))},removeFromCart:e=>{d(s=>{const t=l({},s);return t[e]>1?t[e]-=1:delete t[e],t})},getTotalCartAmount:()=>{let e=0;for(const s in o)if(o[s]>0){let t=u.find(e=>e._id===s);t&&(e+=t.price*o[s])}return e},loadCart:w,saveCartToServer:C,foodData:u,setFoodData:p,food_list:B,isFoodLoading:g,fetchFoodData:A,url:t};return E.jsx(J.Provider,{value:k,children:s.children})},X=()=>{const[s,t]=m.useState(!1),a=m.useRef(null),{user:r,logout:i,token:n,fetchUserProfile:o}=m.useContext(J);m.useEffect(()=>{const e=e=>{a.current&&!a.current.contains(e.target)&&t(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),m.useEffect(()=>{n&&!r&&o()},[n,r,o]),m.useEffect(()=>{},[r,n]);const l=(()=>{try{const e=localStorage.getItem("user");return e?JSON.parse(e):null}catch(e){return null}})(),c=r||l,d=(null==c?void 0:c.name)||"User",u=(null==c?void 0:c.email)||"",p=u?u.charAt(0).toUpperCase():d.charAt(0).toUpperCase();return m.useEffect(()=>{},[r,l,c,d,u,n]),E.jsxs("div",{className:"profile-dropdown-container",ref:a,children:[E.jsx("div",{className:"profile-icon-container",onClick:()=>t(!s),title:`${d} - Click to open menu`,children:E.jsx("div",{className:"profile-letter-avatar",children:p})}),s&&E.jsxs("div",{className:"dropdown-menu",children:[E.jsxs("div",{className:"dropdown-header",children:[E.jsx("p",{className:"user-name",children:d}),E.jsx("p",{className:"user-email",children:u})]}),E.jsxs("div",{className:"dropdown-items",children:[E.jsxs(g,{to:"/profile",className:"dropdown-item",onClick:()=>t(!1),children:[E.jsx("img",{src:R.profile_icon,alt:"Profile"}),E.jsx("span",{children:"Profile"})]}),E.jsxs(g,{to:"/myorders",className:"dropdown-item",onClick:()=>t(!1),children:[E.jsx("img",{src:R.bag_icon,alt:"Orders"}),E.jsx("span",{children:"Orders"})]}),E.jsxs("div",{className:"dropdown-item",onClick:()=>{h(e,null,function*(){n&&(yield o())}),t(!1)},children:[E.jsx("img",{src:R.profile_icon,alt:"Refresh"}),E.jsx("span",{children:"Refresh Profile"})]}),E.jsxs("div",{className:"dropdown-item logout",onClick:()=>{i(),t(!1)},children:[E.jsx("img",{src:R.logout_icon,alt:"Logout"}),E.jsx("span",{children:"Logout"})]})]})]})]})},G=(e,s="https://eatzone.onrender.com",t={})=>{if(!e)return H();const a=String(e).trim();if(!a)return H();if(a.startsWith("http://")||a.startsWith("https://")){if(a.includes("cloudinary.com")){const e=l({format:"auto",quality:"auto:good",width:t.width||400,height:t.height||300,crop:"fill",gravity:"auto"},t);return K(a,e)}return a}return a.startsWith("data:")||a.startsWith("/")?a:a.includes(".png")||a.includes(".jpg")||a.includes(".jpeg")||a.includes(".webp")||a.includes(".gif")?`${s}/images/${a.startsWith("/")?a.substring(1):a}`:H()},H=()=>"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=400&fit=crop&crop=center&auto=format&q=80",K=(e,s={})=>{if(!e||"string"!=typeof e)return e||"";const{width:t=400,height:a=300,quality:r="auto:good",format:i="auto",crop:n="fill",gravity:o="auto"}=s;if(e.includes("/w_")||e.includes("/q_"))return e;const l=e.split("/upload/");if(2!==l.length)return e;const[c,d]=l;return`${c}/upload/${[`w_${t}`,`h_${a}`,`c_${n}`,`g_${o}`,`q_${r}`,`f_${i}`,"fl_progressive","fl_immutable_cache","fl_awebp","dpr_auto"].join(",")}/${d}`},V=e=>{const s=e.target,t=s.src;if(s.dataset.errorCount?s.dataset.errorCount=String(parseInt(s.dataset.errorCount)+1):s.dataset.errorCount="1",!(parseInt(s.dataset.errorCount)>3))return t.includes("cloudinary.com")&&!s.dataset.cloudinaryFallback?(s.dataset.cloudinaryFallback="true",void(s.src=H())):t.includes("/images/")&&!s.dataset.fallbackAttempted?(s.dataset.fallbackAttempted="true",void(s.src=H())):void(s.dataset.finalFallback||(s.dataset.finalFallback="true",s.src=H()));s.src=H()},q=({setShowLogin:s})=>{const[t,a]=m.useState("menu"),{getTotalCartAmount:r,token:i,foodData:n,url:o,addToCart:l,user:c}=m.useContext(J);m.useEffect(()=>{},[i,c]);const[d,u]=m.useState(""),[p,f]=m.useState([]),[j,A]=m.useState(!1),[v,y]=m.useState([]),N=m.useRef(null),w=x();return m.useEffect(()=>{h(e,null,function*(){try{const e=yield fetch(`${o}/api/restaurant/list`),s=yield e.json();s.success&&y(s.data)}catch(e){}})},[o]),m.useEffect(()=>{const e=e=>{N.current&&!N.current.contains(e.target)&&A(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]),E.jsxs("div",{className:"navbar",children:[E.jsx(g,{to:"/",children:E.jsx("img",{src:R.logo,alt:"",className:"logo"})}),E.jsxs("ul",{className:"navbar-menu",children:[E.jsx("li",{children:E.jsx(g,{to:"/",onClick:()=>a("Home"),className:"Home"===t?"active":"",children:"Home"})}),E.jsx("li",{children:E.jsx("a",{href:"#explore-menu",onClick:()=>a("Menu"),className:"Menu"===t?"active":"",children:"Menu"})}),E.jsx("li",{children:E.jsx("a",{href:"#app-download",onClick:()=>a("Mobile-app"),className:"Mobile-app"===t?"active":"",children:"Mobile-app"})}),E.jsx("li",{children:E.jsx("a",{href:"#footer",onClick:()=>a("Contact-us"),className:"Contact-us"===t?"active":"",children:"Contact us"})})]}),E.jsxs("div",{className:"navbar-right",children:[E.jsxs("div",{className:"navbar-search",ref:N,children:[E.jsx("img",{src:R.search_icon,alt:""}),E.jsx("input",{type:"text",placeholder:"Search for restaurants & food items",value:d,onChange:e=>{const s=e.target.value;if(u(s),""===s.trim())return f([]),void A(!1);try{const e=(n||[]).filter(e=>e&&e.name&&e.category&&(e.name.toLowerCase().includes(s.toLowerCase())||e.category.toLowerCase().includes(s.toLowerCase()))).slice(0,5),t=(v||[]).filter(e=>e&&e.name&&(e.name.toLowerCase().includes(s.toLowerCase())||e.description&&e.description.toLowerCase().includes(s.toLowerCase())||e.cuisineTypes&&e.cuisineTypes.some(e=>e&&e.toLowerCase().includes(s.toLowerCase())))).slice(0,3);f([...e,...t]),A(!0)}catch(t){f([]),A(!1)}},onKeyDown:e=>{"Enter"===e.key&&d.trim()&&(w("/"),A(!1),setTimeout(()=>{const e=document.getElementById("food-display");e&&e.scrollIntoView({behavior:"smooth"})},100))},onFocus:()=>d&&A(!0)}),j&&p.length>0&&E.jsxs("div",{className:"search-results-dropdown",children:[p.map((e,s)=>E.jsxs("div",{className:"search-result-item",onClick:()=>(e=>{e.address?w(`/restaurant/${e._id}`):(w("/"),setTimeout(()=>{const e=document.getElementById("food-display");e&&e.scrollIntoView({behavior:"smooth"})},100)),u(""),A(!1)})(e),children:[E.jsx("img",{src:G(e.image,o),alt:e.name,className:"search-result-image",onError:e=>{e.target.src="/api/placeholder/40/40"}}),E.jsxs("div",{className:"search-result-info",children:[E.jsx("h4",{children:e.name}),E.jsx("p",{children:e.address?E.jsxs(E.Fragment,{children:[E.jsx("span",{className:"result-type",children:"Restaurant"}),E.jsxs("span",{className:"result-detail",children:["⭐ ",e.rating," • ",e.deliveryTime]})]}):E.jsxs(E.Fragment,{children:[E.jsx("span",{className:"result-type",children:"Food Item"}),E.jsxs("span",{className:"result-detail",children:[e.category," • ₹",e.price]})]})})]}),!e.address&&E.jsx("button",{className:"search-add-to-cart",onClick:s=>{s.stopPropagation(),l(e._id)},title:"Add to cart",children:E.jsx("img",{src:R.add_icon_white,alt:"Add to cart"})})]},s)),d&&E.jsx("div",{className:"search-result-footer",children:E.jsx("p",{children:"Press Enter or click to search more"})})]})]}),E.jsxs("div",{className:"navbar-search-icon",children:[E.jsx(g,{to:"/cart",children:E.jsx("img",{src:R.basket_icon,alt:""})}),E.jsx("div",{className:0===r()?"":"dot"})]}),i?E.jsx(X,{}):E.jsx("button",{onClick:()=>s(!0),children:"Sign in"})]})]})},Z=()=>E.jsxs("div",{className:"footer",id:"footer",children:[E.jsxs("div",{className:"footer-content",children:[E.jsxs("div",{className:"footer-content-left",children:[E.jsx("img",{src:R.logo,alt:""}),E.jsxs("p",{children:["Craving something delicious? We’ve got you covered! Order now and enjoy fresh, fast, ",E.jsx("br",{}),"and flavorful food delivered to your doorstep."]}),E.jsxs("div",{className:"footer-social-icons",children:[E.jsx("img",{src:R.facebook_icon,alt:""}),E.jsx("img",{src:R.twitter_icon,alt:""}),E.jsx("img",{src:R.linkedin_icon,alt:""})]})]}),E.jsx("div",{className:"footer-content-center",children:E.jsxs("div",{className:"footer-links-container",children:[E.jsxs("div",{className:"footer-company",children:[E.jsx("h2",{children:"COMPANY"}),E.jsxs("ul",{children:[E.jsx("li",{children:"Home"}),E.jsx("li",{children:"About us"}),E.jsx("li",{children:"Delivery"}),E.jsx("li",{children:"Privacy policy"})]})]}),E.jsxs("div",{className:"footer-contact",children:[E.jsx("h2",{children:"GET IN TOUCH"}),E.jsxs("ul",{children:[E.jsx("li",{children:"+91 9876554321"}),E.jsx("li",{children:"<EMAIL>"})]})]})]})})]}),E.jsx("hr",{}),E.jsx("p",{className:"footer-copyright",children:"Copyright 2025 # Eatzone.com - All Reserved."})]}),_=({size:e=20})=>E.jsxs("svg",{width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[E.jsx("path",{d:"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z",fill:"white"}),E.jsx("path",{d:"M19.0003 12.2332C19.0003 11.3699 18.9271 10.7399 18.7705 10.0865H12.2539V13.1732H16.1605C16.0405 13.9065 15.6005 14.9532 14.6005 15.6665L14.5822 15.7833L16.9172 17.6399L17.1039 17.6599C18.4472 16.4399 19.0003 14.5265 19.0003 12.2332Z",fill:"#4285F4"}),E.jsx("path",{d:"M12.2539 19.0001C14.3872 19.0001 16.1739 18.3334 17.1039 17.6601L14.6005 15.6667C13.9539 16.1067 13.1005 16.4001 12.2539 16.4001C10.1205 16.4001 8.32718 14.9867 7.69385 13.0001L7.58368 13.0101L5.16385 14.9401L5.12385 15.0467C6.04385 17.3667 8.92052 19.0001 12.2539 19.0001Z",fill:"#34A853"}),E.jsx("path",{d:"M7.69374 13C7.52707 12.3467 7.43374 11.6533 7.43374 10.9333C7.43374 10.2133 7.52707 9.52 7.68041 8.86667L7.67374 8.74L5.21374 6.77333L5.12374 6.82C4.41374 8.32 4.00041 9.98667 4.00041 10.9333C4.00041 11.88 4.41374 13.5467 5.12374 15.0467L7.69374 13Z",fill:"#FBBC05"}),E.jsx("path",{d:"M12.2539 5.46666C13.6005 5.46666 14.5005 6.06666 15.0005 6.53333L17.2539 4.33333C16.1605 3.33333 14.3872 2.66666 12.2539 2.66666C8.92052 2.66666 6.04385 4.3 5.12385 6.62L7.68052 8.86666C8.32718 6.88 10.1205 5.46666 12.2539 5.46666Z",fill:"#EB4335"})]}),$=({setShowLogin:s})=>{const{url:t,setToken:a}=m.useContext(J),[r,i]=m.useState("Login"),[n,o]=m.useState({name:"",email:"",password:""}),d=e=>{const s=e.target.name,t=e.target.value;o(e=>c(l({},e),{[s]:t}))};return E.jsx("div",{className:"login-popup",children:E.jsxs("form",{onSubmit:i=>h(e,null,function*(){i.preventDefault();try{let e=t;e+="Login"===r?"/api/user/login":"/api/user/register";const i=yield b.post(e,n);i.data.success?(a(i.data.token),i.data.user&&localStorage.setItem("user",JSON.stringify(i.data.user)),s(!1)):alert(i.data.message)}catch(e){alert("An error occurred during login. Please try again.")}}),className:"login-popup-container",children:[E.jsxs("div",{className:"login-popup-title",children:[E.jsx("h2",{children:r}),E.jsx("img",{onClick:()=>s(!1),src:R.cross_icon,alt:""})]}),E.jsxs("div",{className:"login-popup-inputs",children:["Login"===r?E.jsx(E.Fragment,{}):E.jsx("input",{name:"name",onChange:d,value:n.name,type:"text",placeholder:"Your name",required:!0}),E.jsx("input",{name:"email",onChange:d,value:n.email,type:"email",placeholder:"Your email",required:!0}),E.jsx("input",{name:"password",onChange:d,value:n.password,type:"password",placeholder:"Password",required:!0})]}),E.jsx("button",{type:"submit",children:"Sign Up"===r?"Create account":"Login"}),E.jsx("div",{className:"login-popup-divider",children:E.jsx("span",{children:"OR"})}),E.jsxs("button",{type:"button",onClick:()=>{const e=`${t}/api/user/auth/google`;window.location.href=e},className:"google-signin-button",children:[E.jsx(_,{size:20}),E.jsx("span",{children:"Sign in with Google"})]}),E.jsxs("div",{className:"login-popup-condition",children:[E.jsx("input",{type:"checkbox",required:!0}),E.jsx("p",{children:"By continuing, i agree to the terms of use & privacy policy."})]}),"Login"===r?E.jsxs("p",{children:["Create a new account? ",E.jsx("span",{onClick:()=>i("Sign Up"),children:"Click here"})]}):E.jsxs("p",{children:["Already have an account? ",E.jsx("span",{onClick:()=>i("Login"),children:"Login here"})]})]})})},ee=()=>E.jsx("div",{className:"header",children:E.jsxs("div",{className:"header-contents",children:[E.jsx("h2",{children:"Order your favourite food here"}),E.jsx("p",{children:"Choose from a diverse menu featuring a delicious array of dishes crafted with the finest ingredients and elevate your dining experience, one delicious meal at a time."}),E.jsx("button",{onClick:()=>{const e=document.getElementById("explore-menu");e&&e.scrollIntoView({behavior:"smooth"})},children:"View Menu"})]})}),se=new class{constructor(){this.loadTimes=new Map,this.failedImages=new Set,this.totalImages=0,this.loadedImages=0,this.startTime=Date.now(),this.targetLoadTime=3e3}startLoad(e,s="unknown"){this.totalImages++,this.loadTimes.set(e,{startTime:Date.now(),type:s,status:"loading"})}markLoaded(e){const s=this.loadTimes.get(e);if(s){const e=Date.now()-s.startTime;s.loadTime=e,s.status="loaded",this.loadedImages++,this.checkPerformanceTarget()}}markFailed(e,s){const t=this.loadTimes.get(e);t&&(t.status="failed",t.error=s,this.failedImages.add(e))}checkPerformanceTarget(){Date.now(),this.startTime,this.loadedImages,this.totalImages,this.targetLoadTime}getStats(){const e=Date.now()-this.startTime,s=this.loadedImages/this.totalImages*100,t=Array.from(this.loadTimes.values()).filter(e=>e.loadTime).reduce((e,s,t,a)=>e+s.loadTime/a.length,0);return{totalImages:this.totalImages,loadedImages:this.loadedImages,failedImages:this.failedImages.size,loadedPercentage:s.toFixed(1),totalTime:e,avgLoadTime:Math.round(t),targetMet:e<=this.targetLoadTime&&s>=90}}logSummary(){this.getStats().targetMet}},te=100,ae=10,re=new class{constructor(){this.cache=new Map,this.preloadQueue=new Set,this.maxCacheSize=te,this.preloadBatchSize=ae}has(e){return this.cache.has(e)}get(e){const s=this.cache.get(e);return s?(s.lastAccessed=Date.now(),s.img):null}set(e,s){this.cache.size>=this.maxCacheSize&&this.evictOldest(),this.cache.set(e,{img:s,cached:Date.now(),lastAccessed:Date.now()})}preload(e){return new Promise((s,t)=>{if(this.has(e))return void s(this.get(e));if(this.preloadQueue.has(e)){const t=setInterval(()=>{this.has(e)&&(clearInterval(t),s(this.get(e)))},100);return}this.preloadQueue.add(e);const a=new Image;a.crossOrigin="anonymous",a.onload=()=>{this.set(e,a),this.preloadQueue.delete(e),s(a)},a.onerror=()=>{this.preloadQueue.delete(e),t(new Error(`Failed to preload image: ${e}`))},a.src=e})}preloadMultiple(e){return h(this,null,function*(){if(!e||0===e.length)return[];const s=[];for(let r=0;r<e.length;r+=this.preloadBatchSize)s.push(e.slice(r,r+this.preloadBatchSize));const t=[];for(const e of s){const r=e.map(e=>this.preload(e).catch(s=>({error:s,url:e})));try{const e=yield Promise.allSettled(r);t.push(...e),s.length>1&&(yield new Promise(e=>setTimeout(e,25)))}catch(a){}}return t.filter(e=>"fulfilled"===e.status).length,t})}evictOldest(){let e=null,s=Date.now();for(const[t,a]of this.cache.entries())a.lastAccessed<s&&(s=a.lastAccessed,e=t);e&&this.cache.delete(e)}clear(){this.cache.clear(),this.preloadQueue.clear()}getStats(){return{cacheSize:this.cache.size,maxCacheSize:this.maxCacheSize,preloadQueueSize:this.preloadQueue.size,cacheHitRate:this.cacheHits/(this.cacheHits+this.cacheMisses)||0}}},ie=({type:e="spinner",size:s="medium",color:t="#ff6b35",text:a="",className:r=""})=>E.jsxs("div",{className:`loading-indicator ${r}`,children:[(()=>{switch(e){case"dots":return E.jsxs("div",{className:`loading-dots ${s}`,children:[E.jsx("div",{className:"dot",style:{backgroundColor:t}}),E.jsx("div",{className:"dot",style:{backgroundColor:t}}),E.jsx("div",{className:"dot",style:{backgroundColor:t}})]});case"pulse":return E.jsx("div",{className:`loading-pulse ${s}`,style:{backgroundColor:t}});case"skeleton":return E.jsx("div",{className:`skeleton-box ${s}`});default:return E.jsx("div",{className:`loading-spinner ${s}`,style:{borderTopColor:t},children:E.jsx("div",{className:"spinner-inner"})})}})(),a&&E.jsx("span",{className:"loading-text",children:a})]}),ne=e=>{var s=e,{src:t,alt:a,className:r="",width:i=400,height:n=300,quality:o="auto",lazy:h=!0,placeholder:u=!0,onLoad:p,onError:g,style:x={}}=s,f=d(s,["src","alt","className","width","height","quality","lazy","placeholder","onLoad","onError","style"]);const[j,A]=m.useState(!1),[v,y]=m.useState(!h),[N,w]=m.useState(!1),b=m.useRef(null),C=m.useRef(null);m.useEffect(()=>{if(!h)return void y(!0);if(v)return;const e=new IntersectionObserver(s=>{s.forEach(s=>{s.isIntersecting&&(y(!0),e.disconnect())})},{rootMargin:"500px",threshold:.01});return b.current&&(e.observe(b.current),C.current=e),()=>{C.current&&C.current.disconnect()}},[h,v]);const k=G(t,void 0,{width:i,height:n,quality:o,format:"auto",crop:"fill",gravity:"auto"});return m.useEffect(()=>{if(t){const e=r.includes("restaurant")?"restaurant":r.includes("food")?"food":r.includes("category")?"category":"unknown";se.startLoad(k,e)}},[t,k,r]),m.useEffect(()=>{k&&re.has(k)&&(A(!0),w(!1))},[k]),E.jsxs("div",c(l({ref:b,className:`optimized-image-container ${r}`,style:l({width:"auto"===i?"auto":`${i}px`,height:"auto"===n?"auto":`${n}px`},x)},f),{children:[u&&!j&&!N&&E.jsx("div",{className:"image-placeholder",children:h&&!v?E.jsx("div",{className:"placeholder-content",children:E.jsx("span",{className:"placeholder-icon",children:a?a.charAt(0).toUpperCase():"🍽️"})}):E.jsx(ie,{type:"skeleton",size:"medium",className:"image-loading"})}),v&&!N&&E.jsx("img",{src:k,alt:a,className:"optimized-image "+(j?"loaded":"loading"),onLoad:e=>{A(!0),w(!1),se.markLoaded(k),k&&e.target&&re.set(k,e.target),p&&p()},onError:e=>{w(!0),se.markFailed(k,e),g?g(e):V(e)},loading:h?"lazy":"eager",decoding:"async",style:{display:j?"block":"none",width:"100%",height:"100%",objectFit:"cover",transition:r.includes("priority-load")?"opacity 0.1s ease-in-out":"opacity 0.2s ease-in-out",opacity:j?1:0}}),N&&E.jsxs("div",{className:"image-error",children:[E.jsx("span",{className:"error-icon",children:"🍽️"}),E.jsx("span",{className:"error-text",children:"Image unavailable"})]}),!1]}))},oe=({image:e,categoryName:s,baseUrl:t,className:a="",alt:r,onLoad:i,onError:n})=>{const[o,l]=m.useState(!1),[c,d]=m.useState(!0),h=((e,s)=>e?e.startsWith("http")?e:`${s}/images/${e}`:null)(e,t),u=(e=>{const s={Rolls:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/rolls.jpg",Salad:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/salad.jpg",Desserts:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/desserts.jpg",Deserts:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/desserts.jpg",Sandwich:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sandwich.jpg","Sandwiches & Wraps":"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sandwich.jpg",Cake:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/cake.jpg",Veg:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/veg.jpg",Pizza:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pizza.jpg",Pizzas:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pizza.jpg",Pasta:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/pasta.jpg",Noodles:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/noodles.jpg","Noodles & Pasta":"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/noodles.jpg","Main Course":"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/main-course.jpg",Appetizer:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/appetizer.jpg",Sushi:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/sushi.jpg",Burgers:"https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=400&fit=crop","Biryani & Rice":"https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=400&fit=crop",Beverages:"https://images.unsplash.com/photo-**********-7f47ccb76574?w=400&h=400&fit=crop","Salads & Healthy":"https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=400&fit=crop",default:"https://res.cloudinary.com/dodxdudew/image/upload/v1735055000/eatzone/categories/default-food.jpg"};return s[e]||Object.keys(s).find(s=>e.toLowerCase().includes(s.toLowerCase())||s.toLowerCase().includes(e.toLowerCase()))&&s[Object.keys(s).find(s=>e.toLowerCase().includes(s.toLowerCase())||s.toLowerCase().includes(e.toLowerCase()))]||s.default})(s),p=o?u:h||u;return E.jsx("div",{className:`category-image-container ${a}`,children:o?(()=>{const e=s?s.charAt(0).toUpperCase():"?";return E.jsx("div",{style:{width:"100%",height:"100%",background:"linear-gradient(135deg, #ff6b35, #ff8c42)",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontSize:"24px",fontWeight:"bold",borderRadius:"50%"},children:e})})():E.jsx(ne,{src:(g=p,g?g.includes("cloudinary.com")?K(g,{width:80,height:80,quality:"auto:good",format:"auto",crop:"fill",gravity:"auto"}):G(g,void 0,{width:80,height:80,quality:"auto:good"}):H()),alt:r||s,width:80,height:80,quality:"auto:good",lazy:!1,className:"category-image priority-load",onLoad:()=>{d(!1),l(!1),i&&i()},onError:e=>{d(!1),l(!0),n&&n(e)},style:{borderRadius:"50%"}})});var g},le=({width:e="100%",height:s="20px",borderRadius:t="4px",className:a="",variant:r="rectangular",animation:i="pulse",count:n=1,style:o={}})=>{const c=l({width:e,height:s,borderRadius:"circular"===r?"50%":t},o),d=`skeleton ${r} ${i} ${a}`;return 1===n?E.jsx("div",{className:d,style:c}):E.jsx("div",{className:"skeleton-group",children:Array.from({length:n}).map((e,s)=>E.jsx("div",{className:d,style:c},s))})},ce=e=>{var s=e,{lines:t=1,width:a="100%"}=s,r=d(s,["lines","width"]);return E.jsx("div",{className:"skeleton-text-container",children:Array.from({length:t}).map((e,s)=>E.jsx(le,l({width:s===t-1?"70%":a,height:"16px"},r),s))})},de=e=>{var s=e,{width:t="100%",height:a="200px"}=s,r=d(s,["width","height"]);return E.jsx(le,l({width:t,height:a,borderRadius:"8px"},r))},he=e=>{var s=e,{size:t="40px"}=s,a=d(s,["size"]);return E.jsx(le,l({width:t,height:t,variant:"circular"},a))},me=()=>E.jsxs("div",{className:"skeleton-food-item",children:[E.jsx(de,{height:"200px"}),E.jsxs("div",{className:"skeleton-food-content",children:[E.jsx(le,{width:"90%",height:"18px",className:"skeleton-food-title"}),E.jsx(ce,{lines:2}),E.jsxs("div",{className:"skeleton-food-footer",children:[E.jsx(le,{width:"60px",height:"20px"}),E.jsx(le,{width:"30px",height:"30px",variant:"circular"})]})]})]}),ue=()=>E.jsxs("div",{className:"skeleton-restaurant",children:[E.jsx(de,{height:"160px"}),E.jsxs("div",{className:"skeleton-restaurant-content",children:[E.jsx(le,{width:"85%",height:"20px",className:"skeleton-restaurant-title"}),E.jsx(ce,{lines:1}),E.jsxs("div",{className:"skeleton-restaurant-details",children:[E.jsx(le,{width:"50px",height:"16px"}),E.jsx(le,{width:"80px",height:"16px"})]})]})]}),pe=()=>E.jsxs("div",{className:"skeleton-category",children:[E.jsx(he,{size:"80px"}),E.jsx(le,{width:"60px",height:"14px"})]}),ge=(e,s)=>({register:()=>{},unregister:()=>{},reload:(e=!1)=>Promise.resolve()}),xe=()=>{},fe=({category:s,setCategory:t})=>{const[a,r]=m.useState([]),[i,n]=m.useState(!0),[o,l]=m.useState(null),c=m.useCallback(()=>h(e,null,function*(){try{n(!0),l(null);const e=yield W.getCategories();e.success?r(e.data):l(e.message||"Failed to load categories")}catch(e){l("Unable to load categories. Please try again.")}finally{n(!1)}}),[]),{register:d,unregister:u}=ge();return m.useEffect(()=>{let s=!0;return h(e,null,function*(){s&&(yield c(),d())}),()=>{s=!1,u()}},[c,d,u]),m.useEffect(()=>{xe()},[]),E.jsxs("div",{className:"explore-menu",id:"explore-menu",children:[E.jsx("h1",{children:"Explore our menu"}),E.jsx("p",{className:"explore-menu-text",children:"Choose from a diverse menu featuring a delection array of dishes crafted with the finest ingredients and carvings and elevate your dining experience,one delicious meal at a time."}),i?E.jsx("div",{className:"skeleton-grid category-grid",children:Array.from({length:8}).map((e,s)=>E.jsx(pe,{},s))}):o?E.jsxs("div",{style:{textAlign:"center",padding:"40px 20px",color:"#666"},children:[E.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"⚠️"}),E.jsx("h3",{style:{margin:"0 0 8px 0",color:"#ff6b35"},children:"Failed to load categories"}),E.jsx("p",{style:{margin:"0 0 16px 0",color:"#666"},children:o}),E.jsx("button",{onClick:()=>c(),style:{padding:"10px 20px",backgroundColor:"#ff6b35",color:"white",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Try Again"})]}):0===a.length?E.jsxs("div",{style:{textAlign:"center",padding:"40px 20px",color:"#666"},children:[E.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"🍽️"}),E.jsx("h3",{style:{margin:"0 0 8px 0",color:"#333"},children:"No categories available"}),E.jsx("p",{style:{margin:"0",color:"#666"},children:"Categories will appear here once they are added by the admin."})]}):E.jsx("div",{className:"explore-menu-list",children:a.map(e=>E.jsx("div",{onClick:()=>t(s=>s===e.name?"All":e.name),className:"explore-menu-list-item",children:E.jsx(oe,{image:e.image,categoryName:e.name,baseUrl:"https://eatzone.onrender.com",className:s===e.name?"active":"",alt:e.name})},e._id))}),E.jsx("hr",{})]})},je=e=>`₹${e}`,Ae=({id:e,name:s,price:t,description:a,image:r,originalPrice:i,discountPercentage:n,isOnSale:o,discountLabel:l,isPopular:c,isFeatured:d,tags:h})=>{const{cartItems:u,addToCart:p,removeFromCart:g}=m.useContext(J);return E.jsxs("div",{className:"food-item",children:[E.jsxs("div",{className:"food-item-img-container",children:[E.jsx(ne,{src:(x=r,x?x.includes("cloudinary.com")?K(x,{width:280,height:200,quality:"auto:good",format:"auto",crop:"fill",gravity:"auto"}):G(x,void 0,{width:280,height:200,quality:"auto:good"}):H()),alt:s,width:280,height:200,quality:"auto:good",lazy:!1,className:"food-item-image priority-load",onLoad:()=>{},onError:e=>{}}),o&&n>0&&E.jsx("div",{className:"discount-badge "+(n>=25?"mega-deal":""),children:l||`${n}% OFF`}),(c||d)&&E.jsxs("div",{className:"item-badges",children:[c&&E.jsx("span",{className:"badge popular",children:"🔥 Popular"}),d&&E.jsx("span",{className:"badge featured",children:"⭐ Featured"})]}),u[e]?E.jsxs("div",{className:"food-item-counter",children:[E.jsx("img",{onClick:()=>g(e),src:R.remove_icon_red,alt:"Remove from cart"}),E.jsx("p",{children:u[e]}),E.jsx("img",{onClick:()=>p(e),src:R.add_icon_green,alt:"Add more to cart"})]}):E.jsx("img",{className:"add",onClick:()=>p(e),src:R.add_icon_white,alt:"Add to cart"})]}),E.jsxs("div",{className:"food-item-info",children:[E.jsxs("div",{className:"food-item-name-rating",children:[E.jsx("p",{children:s}),E.jsx("img",{src:R.rating_stars,alt:""})]}),E.jsx("p",{className:"food-item-desc",children:a}),h&&h.length>0&&E.jsx("div",{className:"food-item-tags",children:h.map((e,s)=>E.jsx("span",{className:"tag",children:e},s))}),E.jsx("div",{className:"food-item-price-container",children:o&&i?E.jsx("div",{className:"price-with-discount",children:E.jsxs("div",{className:"price-row",children:[E.jsxs("div",{className:"price-info",children:[E.jsx("span",{className:"original-price",children:je(i)}),E.jsx("span",{className:"discounted-price",children:je(t)})]}),E.jsxs("span",{className:"savings",children:["You Save ",je(i-t),"!"]})]})}):E.jsx("p",{className:"food-item-price",children:je(t)})})]})]});var x},ve=({category:e})=>{const{food_list:s,foodData:t,isFoodLoading:a}=m.useContext(J),r=t&&t.length>0?t:s||[];return E.jsxs("div",{className:"food-display",id:"food-display",children:[E.jsx("h2",{children:"Top dishes near you"}),a?E.jsx("div",{className:"skeleton-grid food-grid",children:Array.from({length:8}).map((e,s)=>E.jsx(me,{},s))}):r&&r.length>0?E.jsx("div",{className:"food-display-list",children:r.map((s,t)=>"All"===e||e===s.category?E.jsx(Ae,{id:s._id,name:s.name,description:s.description,price:s.price,image:s.image,originalPrice:s.originalPrice,discountPercentage:s.discountPercentage,isOnSale:s.isOnSale,discountLabel:s.discountLabel,isPopular:s.isPopular,isFeatured:s.isFeatured,tags:s.tags},t):null)}):E.jsxs("div",{style:{textAlign:"center",padding:"40px 20px",color:"#666"},children:[E.jsx("div",{style:{fontSize:"48px",marginBottom:"16px"},children:"🍽️"}),E.jsx("h3",{style:{margin:"0 0 8px 0",color:"#333"},children:"No food items available"}),E.jsx("p",{style:{margin:"0",color:"#666"},children:"Food items will appear here once they are added by the admin."})]})]})},ye=({restaurant:e})=>{const s=x();return E.jsxs("div",{className:"restaurant-card",onClick:()=>{s(`/restaurant/${e._id}`)},children:[E.jsxs("div",{className:"restaurant-image",children:[E.jsx(ne,{src:(t=e.image,t?t.includes("cloudinary.com")?K(t,{width:320,height:200,quality:"auto:good",format:"auto",crop:"fill",gravity:"auto"}):G(t,void 0,{width:320,height:200,quality:"auto:good"}):H()),alt:e.name,width:320,height:200,quality:"auto:good",lazy:!1,className:"restaurant-img priority-load",onLoad:()=>{},onError:e=>{}}),E.jsx("div",{className:"delivery-time",children:E.jsx("span",{children:e.deliveryTime})})]}),E.jsxs("div",{className:"restaurant-info",children:[E.jsx("h3",{className:"restaurant-name",children:e.name}),E.jsx("p",{className:"restaurant-description",children:e.description}),E.jsxs("div",{className:"restaurant-details",children:[E.jsxs("div",{className:"rating",children:[E.jsx("span",{className:"rating-star",children:"⭐"}),E.jsx("span",{className:"rating-value",children:e.rating})]}),E.jsx("div",{className:"delivery-info",children:E.jsx("span",{className:"delivery-fee",children:0===e.deliveryFee?"Free Delivery":`₹${e.deliveryFee} delivery`})})]}),e.cuisineTypes&&e.cuisineTypes.length>0&&E.jsxs("div",{className:"cuisine-types",children:[e.cuisineTypes.slice(0,3).map((e,s)=>E.jsx("span",{className:"cuisine-tag",children:e},s)),e.cuisineTypes.length>3&&E.jsxs("span",{className:"cuisine-tag more",children:["+",e.cuisineTypes.length-3]})]}),e.minimumOrder>0&&E.jsx("div",{className:"minimum-order",children:E.jsxs("span",{children:["Min order: ₹",e.minimumOrder]})})]})]});var t},Ne=()=>{const[s,t]=m.useState([]),[a,r]=m.useState(!0),[i,n]=m.useState(null),o=m.useCallback(()=>h(e,null,function*(){try{r(!0),n(null);const e=yield W.getRestaurants();e.success?t(e.data):n(e.message||"Failed to fetch restaurants")}catch(e){n("Failed to load restaurants")}finally{r(!1)}}),[]),{register:l,unregister:c}=ge();return m.useEffect(()=>{let s=!0;return h(e,null,function*(){s&&(yield o(),l())}),()=>{s=!1,c()}},[]),a?E.jsxs("div",{className:"restaurant-list-container",children:[E.jsxs("div",{className:"restaurant-list-header",children:[E.jsx("div",{className:"header-icon",children:"🍽️"}),E.jsx("h2",{children:"Popular Restaurants "}),E.jsx("p",{className:"restaurant-subtitle",children:"Loading amazing dining experiences..."})]}),E.jsx("div",{className:"skeleton-grid restaurant-grid",children:Array.from({length:6}).map((e,s)=>E.jsx(ue,{},s))})]}):i?E.jsxs("div",{className:"restaurant-list-container",children:[E.jsx("h2",{children:"Popular Restaurants "}),E.jsxs("div",{className:"restaurant-error",children:[E.jsxs("p",{children:["😕 ",i]}),E.jsx("button",{onClick:o,className:"retry-btn",children:"Try Again"})]})]}):0===s.length?E.jsxs("div",{className:"restaurant-list-container",children:[E.jsx("h2",{children:"Popular Restaurants"}),E.jsxs("div",{className:"no-restaurants",children:[E.jsx("p",{children:"🍽️ No restaurants available at the moment"}),E.jsx("p",{children:"Check back later for delicious options!"})]})]}):E.jsxs("div",{className:"restaurant-list-container",id:"restaurants",children:[E.jsxs("div",{className:"restaurant-list-header",children:[E.jsx("div",{className:"header-icon",children:" 🍽️"}),E.jsx("h2",{children:"Popular Restaurants "}),E.jsx("p",{className:"restaurant-subtitle",children:"Discover amazing dining experiences"}),E.jsxs("div",{className:"header-bottom",children:[E.jsxs("p",{className:"restaurant-count",children:[s.length," restaurants available"]}),s.length>3&&E.jsx("div",{className:"scroll-hint",children:E.jsx("span",{children:"👈 Scroll to see more 👉"})})]})]}),E.jsx("div",{className:"restaurant-grid",children:s.map(e=>E.jsx(ye,{restaurant:e},e._id))}),E.jsx("div",{className:"section-divider",children:E.jsx("hr",{})})]})},we=()=>E.jsx("div",{children:E.jsxs("div",{className:"app-download",id:"app-download",children:[E.jsxs("p",{children:["For Beter Experience Download ",E.jsx("br",{}),"EatZone App"]}),E.jsxs("div",{className:"app-download-platforms",children:[E.jsx("img",{src:R.play_store,alt:""}),E.jsx("img",{src:R.app_store,alt:""})]})]})}),be="https://eatzone.onrender.com".replace(/\/$/,""),Ce={CHATBOT:"/api/chatbot/chat"},ke={"Content-Type":"application/json"},Se=(e=null)=>{const s=l({},ke),t=e||localStorage.getItem("token");return t&&(s.Authorization=`Bearer ${t}`),s},Ee=(s,...t)=>h(e,[s,...t],function*(e,s={}){const t=(e=>`${be}${e}`)(e),a=c(l({},s),{headers:l(l({},ke),s.headers)});try{const e=yield fetch(t,a);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return yield e.json()}catch(r){throw r}}),Ie=(s,t="support",a=null)=>h(e,null,function*(){return Ee(Ce.CHATBOT,{method:"POST",headers:Se(a),body:JSON.stringify({message:s,chatMode:t})})}),Re=()=>{const{user:s,token:t,addToCart:a,food_list:r}=m.useContext(J),[i,n]=m.useState(!1),[o,l]=m.useState([]),[c,d]=m.useState(""),[u,p]=m.useState(!1),[g,x]=m.useState(null),f=e=>{const s=/ITEM:([^|]+)\|PRICE:₹([^|]+)\|CATEGORY:([^|\s]+)/g,t=/\[Add to Cart - ([^\]]+)\]/g,a=[],i=[];let n;for(;null!==(n=s.exec(e));){const e=n[1].trim(),s=n[2].trim(),t=n[3].trim(),i=r.find(s=>s.name.toLowerCase()===e.toLowerCase());i&&a.push({id:i._id,name:e,price:s,category:t,foodItem:i})}for(;null!==(n=t.exec(e));){const e=n[1],s=r.find(s=>s._id===e);s&&i.push({itemId:e,foodItem:s})}return{cleanText:e.replace(s,"").replace(t,"").replace(/\s+/g," ").trim(),items:a,cartButtons:i}},j=e=>{a(e._id),l(s=>[...s,{role:"bot",text:`✅ ${e.name} added to cart!`,isConfirmation:!0}])},A=e=>{x(e),l([{role:"bot",text:"support"===e?"Hello! I'm your Eatzone support assistant. How can I help you today?":"recommendation"===e?"What are you craving today? I can recommend some delicious options from our menu!":"feedback"===e?"I'd love to hear about your Eatzone experience! Please share your feedback.":"Hi! How can I help you today?"}])},v=()=>h(e,null,function*(){if(!c.trim()||!g)return;const e={role:"user",text:c};l(s=>[...s,e]),d(""),p(!0);try{const e=yield Ie(c,g,t),{cleanText:s,items:a,cartButtons:r}=f(e.reply),i={role:"bot",text:s||e.reply,items:a.length>0?a:null,cartButtons:r.length>0?r:null};l(e=>[...e,i])}catch(s){l(e=>[...e,{role:"bot",text:"Something went wrong. Please try again."}])}finally{p(!1)}});return E.jsxs(E.Fragment,{children:[E.jsx("div",{className:"floating-chat-button "+(i?"open":""),onClick:()=>{n(!i),i||(l([]),x(null))},children:i?E.jsx("span",{className:"close-icon",children:"✕"}):E.jsx("span",{className:"chat-icon",children:"💬"})}),i&&E.jsxs("div",{className:"floating-chat-window",children:[E.jsxs("div",{className:"floating-chat-header",children:[E.jsx("span",{children:"🍽️ Eatzone Assistant"}),g&&E.jsx("button",{className:"back-button",onClick:()=>{x(null),l([])},children:"← Back"})]}),E.jsx("div",{className:"floating-chat-content",children:g?E.jsxs(E.Fragment,{children:[E.jsxs("div",{className:"floating-chat-messages",children:[o.map((e,s)=>E.jsxs("div",{children:[E.jsx("div",{className:`floating-chat-bubble ${"user"===e.role?"user":"bot"} ${e.isConfirmation?"confirmation":""}`,children:e.text}),e.items&&e.items.length>0&&E.jsx("div",{className:"food-items-container",children:e.items.map((e,s)=>E.jsxs("div",{className:"food-item-card",children:[E.jsxs("div",{className:"food-item-info",children:[E.jsx("span",{className:"food-item-name",children:e.name}),E.jsxs("span",{className:"food-item-price",children:["₹",e.price]}),E.jsx("span",{className:"food-item-category",children:e.category})]}),E.jsx("button",{className:"add-to-cart-btn",onClick:()=>j(e.foodItem),children:"Add to Cart"})]},s))}),e.cartButtons&&e.cartButtons.length>0&&E.jsx("div",{className:"cart-buttons-container",children:e.cartButtons.map((e,s)=>E.jsxs("div",{className:"cart-button-item",children:[E.jsxs("div",{className:"cart-item-info",children:[E.jsx("img",{src:`https://eatzone.onrender.com/images/${e.foodItem.image}`,alt:e.foodItem.name,className:"cart-item-image"}),E.jsxs("div",{className:"cart-item-details",children:[E.jsx("span",{className:"cart-item-name",children:e.foodItem.name}),E.jsxs("span",{className:"cart-item-price",children:["₹",e.foodItem.price]}),E.jsx("span",{className:"cart-item-category",children:e.foodItem.category})]})]}),E.jsx("button",{className:"add-to-cart-btn-new",onClick:()=>j(e.foodItem),children:"🛒 Add to Cart"})]},s))})]},s)),u&&E.jsx("div",{className:"floating-chat-bubble bot",children:"Typing..."})]}),1===o.length&&E.jsxs("div",{className:"quick-suggestions",children:[E.jsx("p",{className:"suggestions-label",children:"Quick options:"}),E.jsx("div",{className:"suggestion-buttons",children:("support"===g?["Order status","Cancel my order","Refund request","How much time for delivery"]:"recommendation"===g?["Recommend me food","Spicy food","Something light","Sweet desserts"]:"feedback"===g?["Food quality feedback","Delivery feedback","App experience","Overall rating"]:[]).map((s,a)=>E.jsx("button",{className:"suggestion-btn",onClick:()=>(s=>h(e,null,function*(){if(!g)return;const e={role:"user",text:s};l(s=>[...s,e]),p(!0);try{const e=yield Ie(s,g,t),{cleanText:a,items:r,cartButtons:i}=f(e.reply),n={role:"bot",text:a||e.reply,items:r.length>0?r:null,cartButtons:i.length>0?i:null};l(e=>[...e,n])}catch(a){l(e=>[...e,{role:"bot",text:"Something went wrong. Please try again."}])}finally{p(!1)}}))(s),children:s},a))})]}),E.jsxs("div",{className:"floating-chat-input",children:[E.jsx("input",{type:"text",placeholder:"Type your message...",value:c,onChange:e=>d(e.target.value),onKeyDown:e=>{"Enter"===e.key&&v()}}),E.jsx("button",{onClick:v,disabled:!c.trim(),children:"Send"})]})]}):E.jsxs("div",{className:"chat-mode-selection",children:[E.jsx("h3",{children:"How can I help you today?"}),E.jsxs("div",{className:"mode-buttons",children:[E.jsxs("button",{className:"mode-button support",onClick:()=>A("support"),children:[E.jsx("span",{className:"mode-icon",children:"🎧"}),E.jsxs("div",{children:[E.jsx("strong",{children:"Customer Support"}),E.jsx("p",{children:"Get help with orders, account issues, or general questions"})]})]}),E.jsxs("button",{className:"mode-button recommendations",onClick:()=>A("recommendation"),children:[E.jsx("span",{className:"mode-icon",children:"🍕"}),E.jsxs("div",{children:[E.jsx("strong",{children:"Food Recommendations"}),E.jsx("p",{children:"Discover new dishes and get personalized suggestions"})]})]}),E.jsxs("button",{className:"mode-button feedback",onClick:()=>A("feedback"),children:[E.jsx("span",{className:"mode-icon",children:"📝"}),E.jsxs("div",{children:[E.jsx("strong",{children:"Share Feedback"}),E.jsx("p",{children:"Tell us about your experience and help us improve"})]})]})]}),E.jsxs("div",{className:"quick-actions",children:[E.jsx("p",{className:"quick-actions-label",children:"Quick Actions:"}),E.jsxs("div",{className:"quick-action-buttons",children:[E.jsx("button",{className:"quick-action-btn",onClick:()=>{A("support"),setTimeout(()=>{d("I need help with my recent order")},100)},children:"📦 Order Help"}),E.jsx("button",{className:"quick-action-btn",onClick:()=>{A("recommendation"),setTimeout(()=>{d("What's popular today?")},100)},children:"🔥 Popular Items"}),E.jsx("button",{className:"quick-action-btn",onClick:()=>{A("feedback"),setTimeout(()=>{d("I want to share feedback")},100)},children:"📝 Give Feedback"})]})]})]})})]})]})};function Be(){const[e,s]=m.useState("All");return E.jsxs("div",{className:"home-container",children:[E.jsx(ee,{}),E.jsx(Ne,{}),E.jsx(fe,{category:e,setCategory:s}),E.jsx(ve,{category:e}),E.jsx(we,{}),E.jsx(Re,{})]})}const Te=()=>{const{cartItems:e,food_list:s,foodData:t,isFoodLoading:a,removeFromCart:r,getTotalCartAmount:i,clearCart:n,isCartLoading:o,token:l,url:c}=m.useContext(J),d=x(),h=m.useMemo(()=>Object.keys(e).filter(s=>e[s]>0),[e]),u=m.useMemo(()=>h.length>0,[h]);return E.jsxs("div",{className:"cart",children:[E.jsxs("div",{className:"cart-items",children:[E.jsxs("div",{className:"cart-header",children:[E.jsxs("div",{className:"cart-items-title",children:[E.jsx("p",{children:"Items"}),E.jsx("p",{children:"Title"}),E.jsx("p",{children:"Price"}),E.jsx("p",{children:"Quantity"}),E.jsx("p",{children:"Total"}),E.jsx("p",{children:"Remove"})]}),u&&E.jsx("button",{className:"clear-cart-btn",onClick:n,children:"Clear Cart"})]}),E.jsx("br",{}),E.jsx("hr",{}),o||a?E.jsxs("div",{className:"cart-loading",children:[E.jsx("div",{className:"loading-spinner"}),E.jsx("p",{children:"Loading your cart..."})]}):u?E.jsx("div",{className:"cart-items-list",children:h.map(a=>{const i=t.find(e=>e._id===a)||s.find(e=>e._id===a);if(!i)return null;const n=G(i.image,c);return E.jsxs("div",{className:"cart-item-wrapper",children:[E.jsxs("div",{className:"cart-items-item",children:[E.jsx("img",{src:n,alt:i.name,onError:e=>V(e,i.image)}),E.jsx("p",{children:i.name}),E.jsx("p",{children:je(i.price)}),E.jsx("p",{children:e[a]}),E.jsx("p",{children:je(i.price*e[a])}),E.jsx("p",{onClick:()=>r(a),className:"cross",children:"X"})]}),E.jsx("hr",{})]},`cart-item-${a}`)})}):E.jsx("div",{className:"empty-cart",children:E.jsxs("div",{className:"empty-cart-content",children:[E.jsx("div",{className:"empty-cart-icon",children:"🛒"}),E.jsx("h3",{children:"No items present in cart"}),E.jsx("p",{children:"Add some delicious items to your cart to get started!"}),E.jsx("button",{className:"browse-menu-btn",onClick:()=>d("/"),children:"Browse Menu"})]})})]}),u&&E.jsxs("div",{className:"cart-bottom",children:[E.jsxs("div",{className:"cart-total",children:[E.jsx("h2",{children:"Cart Totals"}),E.jsxs("div",{children:[E.jsxs("div",{className:"cart-total-details",children:[E.jsx("p",{children:"Subtotal"}),E.jsx("p",{children:je(i())})]}),E.jsx("hr",{}),E.jsxs("div",{className:"cart-total-details",children:[E.jsx("p",{children:"Delivery Fee"}),E.jsx("p",{children:je(0===i()?0:50)})]}),E.jsx("hr",{}),E.jsxs("div",{className:"cart-total-details",children:[E.jsx("b",{children:"Total"}),E.jsx("b",{children:je(0===i()?0:i()+50)})]})]}),E.jsx("button",{onClick:()=>{l?d("/order"):alert("Please sign in to proceed with checkout")},children:"PROCEED TO CHECKOUT"})]}),E.jsx("div",{className:"cart-promocode",children:E.jsxs("div",{children:[E.jsx("p",{children:"If you have a promo code, Enter it here"}),E.jsxs("div",{className:"cart-promocode-input",children:[E.jsx("input",{type:"text",placeholder:"promocode"}),E.jsx("button",{children:"Submit"})]})]})})]})]})},Fe=()=>{const{getTotalCartAmount:s,cartItems:t,food_list:a,foodData:r,url:i,token:n,user:o}=m.useContext(J),[d,u]=m.useState({firstName:"",lastName:"",email:"",street:"",city:"",state:"",zipCode:"",country:"",phone:""}),[p,g]=m.useState(!1);return E.jsxs("form",{onSubmit:l=>{l.preventDefault(),h(e,null,function*(){if(!n)return void alert("Please login to place an order!");if(0===s())return void alert("Your cart is empty!");const e=document.querySelector(".place-order");if(e.checkValidity()){g(!0);try{const e=Object.keys(t).filter(e=>t[e]>0).map(e=>{const s=r.find(s=>s._id===e)||a.find(s=>s._id===e);if(!s)throw new Error(`Food item with ID ${e} not found`);return{_id:s._id,name:s.name,price:s.price,quantity:t[e]}});if(!o||!o.id&&!o._id)return void alert("User information not available. Please try logging in again.");const l={userId:o.id||o._id,items:e,amount:s()+50,address:{firstName:d.firstName,lastName:d.lastName,email:d.email,phone:d.phone,street:d.street,city:d.city,state:d.state,zipCode:d.zipCode,country:d.country}},c=yield b.post(`${i}/api/order/place`,l,{headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"}});c.data.success?window.location.href=c.data.session_url:alert("Failed to create order: "+(c.data.message||"Unknown error"))}catch(l){l.response?alert("Error: "+(l.response.data.message||"Failed to place order")):alert("There was an error placing your order. Please try again.")}finally{g(!1)}}else e.reportValidity()})},className:"place-order",children:[E.jsxs("div",{className:"place-order-left",children:[E.jsx("p",{className:"title",children:"Delivery Information"}),E.jsxs("div",{className:"multi-fields",children:[E.jsx("input",{name:"firstName",type:"text",placeholder:"First name",value:d.firstName,onChange:e=>u(c(l({},d),{firstName:e.target.value})),required:!0}),E.jsx("input",{name:"lastName",type:"text",placeholder:"Last name",value:d.lastName,onChange:e=>u(c(l({},d),{lastName:e.target.value})),required:!0})]}),E.jsx("input",{name:"email",type:"email",placeholder:"Email address",value:d.email,onChange:e=>u(c(l({},d),{email:e.target.value})),required:!0}),E.jsx("input",{name:"street",type:"text",placeholder:"Street",value:d.street,onChange:e=>u(c(l({},d),{street:e.target.value})),required:!0}),E.jsxs("div",{className:"multi-fields",children:[E.jsx("input",{name:"city",type:"text",placeholder:"City",value:d.city,onChange:e=>u(c(l({},d),{city:e.target.value})),required:!0}),E.jsx("input",{name:"state",type:"text",placeholder:"State",value:d.state,onChange:e=>u(c(l({},d),{state:e.target.value})),required:!0})]}),E.jsxs("div",{className:"multi-fields",children:[E.jsx("input",{name:"zipCode",type:"text",placeholder:"Zip code",value:d.zipCode,onChange:e=>u(c(l({},d),{zipCode:e.target.value})),required:!0}),E.jsx("input",{name:"country",type:"text",placeholder:"Country",value:d.country,onChange:e=>u(c(l({},d),{country:e.target.value})),required:!0})]}),E.jsx("input",{name:"phone",type:"text",placeholder:"Phone",value:d.phone,onChange:e=>u(c(l({},d),{phone:e.target.value})),required:!0})]}),E.jsx("div",{className:"place-order-right",children:E.jsxs("div",{className:"cart-total",children:[E.jsx("h2",{children:"Cart Totals"}),E.jsxs("div",{children:[E.jsxs("div",{className:"cart-total-details",children:[E.jsx("p",{children:"Subtotal"}),E.jsx("p",{children:je(s())})]}),E.jsx("hr",{}),E.jsxs("div",{className:"cart-total-details",children:[E.jsx("p",{children:"Delivery Fee"}),E.jsx("p",{children:je(0===s()?0:50)})]}),E.jsx("hr",{}),E.jsxs("div",{className:"cart-total-details",children:[E.jsx("b",{children:"Total"}),E.jsx("b",{children:je(0===s()?0:s()+50)})]})]}),E.jsx("div",{style:{fontSize:"12px",color:"#666",marginTop:"10px",padding:"8px",backgroundColor:"#f8f9fa",borderRadius:"4px",textAlign:"center"},children:"💳 Payments are processed securely in INR (₹) through Stripe"}),E.jsx("button",{type:"submit",disabled:p,children:p?"PROCESSING...":"PROCEED TO PAYMENT"})]})})]})},Pe=()=>{const{user:s,isUserLoading:t,fetchUserProfile:a,token:r}=m.useContext(J),[i,n]=m.useState(!1);m.useEffect(()=>{r&&(s&&s.name||o())},[r,s]);const o=()=>h(e,null,function*(){if(r){n(!0);try{yield a()}catch(e){}finally{n(!1)}}}),l=t||i,[c,d]=m.useState(null);m.useEffect(()=>{if(!s&&r){const s=localStorage.getItem("user");if(s)try{const e=JSON.parse(s);d(e)}catch(e){}}},[s,r]);const u=s||c,p=(null==u?void 0:u.name)||"User",g=(null==u?void 0:u.email)||"",x=g?g.charAt(0).toUpperCase():p.charAt(0).toUpperCase();return E.jsxs("div",{className:"profile-container",children:[E.jsxs("div",{className:"profile-header",children:[E.jsx("h1",{children:"My Profile"}),r&&E.jsx("button",{onClick:o,disabled:l,className:"refresh-button",children:l?"Refreshing...":"Refresh Profile"})]}),l?E.jsx("div",{className:"loading-container",children:E.jsx("p",{children:"Loading profile data..."})}):r?u?E.jsxs("div",{className:"profile-content",children:[E.jsxs("div",{className:"profile-image-section",children:[E.jsx("div",{className:"profile-image-placeholder",children:x}),E.jsx("h2",{children:p}),E.jsx("p",{children:g})]}),E.jsxs("div",{className:"profile-details",children:[E.jsxs("div",{className:"profile-section",children:[E.jsx("h3",{children:"Account Information"}),E.jsxs("div",{className:"profile-info-item",children:[E.jsx("span",{className:"info-label",children:"Name:"}),E.jsx("span",{className:"info-value",children:p})]}),E.jsxs("div",{className:"profile-info-item",children:[E.jsx("span",{className:"info-label",children:"Email:"}),E.jsx("span",{className:"info-value",children:g||"Not provided"})]}),E.jsxs("div",{className:"profile-info-item",children:[E.jsx("span",{className:"info-label",children:"Account Type:"}),E.jsx("span",{className:"info-value",children:(null==u?void 0:u.googleId)?"Google Account":"Email Account"})]})]}),E.jsxs("div",{className:"profile-section",children:[E.jsx("h3",{children:"Preferences"}),E.jsx("p",{children:"Profile preferences will be added in a future update."})]})]})]}):E.jsxs("div",{className:"error-container",children:[E.jsx("p",{children:"Could not load profile data."}),E.jsx("p",{children:'Please try refreshing the page or click the "Refresh Profile" button.'})]}):E.jsxs("div",{className:"error-container",children:[E.jsx("p",{children:"You need to be logged in to view your profile."}),E.jsx("p",{children:"Please log in and try again."})]})]})},ze=()=>{const{token:s,url:t}=m.useContext(J),[a,r]=m.useState([]),[i,n]=m.useState(!0),[o,l]=m.useState(null),c=m.useCallback(()=>h(e,null,function*(){if(!s)return l("Please login to view your orders"),void n(!1);try{const e=yield b.post(`${t}/api/order/userorders`,{},{headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(e.data.success){const s=(e.data.data||[]).sort((e,s)=>new Date(s.date)-new Date(e.date));r(s)}else l(e.data.message||"Failed to fetch orders")}catch(e){e.response?l(e.response.data.message||"Failed to fetch orders"):l("Network error. Please try again.")}finally{n(!1)}}),[s,t]);return m.useEffect(()=>{c()},[c]),i?E.jsxs("div",{className:"orders-container",children:[E.jsx("div",{className:"orders-header",children:E.jsx("h2",{children:"My Orders"})}),E.jsx("div",{className:"orders-content",children:E.jsx("div",{className:"loading",children:E.jsx("p",{children:"Loading your orders..."})})})]}):o?E.jsxs("div",{className:"orders-container",children:[E.jsx("div",{className:"orders-header",children:E.jsx("h1",{children:"My Orders"})}),E.jsx("div",{className:"orders-content",children:E.jsxs("div",{className:"error",children:[E.jsxs("p",{children:["Error: ",o]}),E.jsx("button",{onClick:c,children:"Try Again"})]})})]}):E.jsxs("div",{className:"orders-container",children:[E.jsx("div",{className:"orders-header",children:E.jsx("h1",{children:"My Orders"})}),E.jsx("div",{className:"orders-content",children:a.length>0?E.jsx("div",{className:"orders-list",children:a.map(e=>E.jsxs("div",{className:"order-card",children:[E.jsxs("div",{className:"order-card-header",children:[E.jsx("div",{className:"order-icon",children:"📦"}),E.jsxs("div",{className:"order-details",children:[E.jsx("div",{className:"order-items-text",children:e.items&&e.items.length>0?e.items.map((s,t)=>E.jsxs("span",{children:[s.name,s.quantity>1&&` x ${s.quantity}`,t<e.items.length-1?", ":""]},s._id||t)):"No items"}),E.jsxs("div",{className:"order-items-count",children:["Items: ",e.items?e.items.reduce((e,s)=>e+(s.quantity||0),0):0]})]}),E.jsx("div",{className:"order-amount",children:je(e.amount||0)})]}),E.jsxs("div",{className:"order-card-footer",children:[E.jsx("div",{className:"order-status",children:E.jsxs("span",{className:`status-badge ${e.status?e.status.toLowerCase().replace(/\s+/g,"-"):"pending"}`,children:["● ",e.status||"Pending"]})}),E.jsx("div",{className:"order-actions",children:E.jsx("button",{className:"track-order-btn",children:"Track Order"})})]})]},e._id))}):E.jsxs("div",{className:"no-orders",children:[E.jsx("div",{className:"no-orders-icon",children:"📦"}),E.jsx("h2",{children:"No Orders Yet"}),E.jsx("p",{children:"You haven't placed any orders yet. Start ordering your favorite food!"}),E.jsx("button",{className:"browse-menu-button",onClick:()=>window.location.href="/#explore-menu",children:"Browse Menu"})]})})]})},Le=()=>{const{id:s}=f(),t=x(),{url:a}=m.useContext(J),[r,i]=m.useState(null),[n,o]=m.useState([]),[l,c]=m.useState([]),[d,u]=m.useState("All"),[p,g]=m.useState(!0),[j,A]=m.useState(null);m.useEffect(()=>{s&&v()},[s]),m.useEffect(()=>{s&&y()},[s,d]);const v=()=>h(e,null,function*(){try{const[e,t]=yield Promise.all([fetch(`${a}/api/restaurant/${s}`),fetch(`${a}/api/restaurant/${s}/categories`)]),r=yield e.json(),n=yield t.json();r.success?i(r.data):A("Restaurant not found"),n.success&&c(["All",...n.data])}catch(e){A("Failed to load restaurant")}}),y=()=>h(e,null,function*(){try{g(!0);const e=yield fetch(`${a}/api/restaurant/${s}/food-items${"All"!==d?`?category=${d}`:""}`),t=yield e.json();t.success?o(t.data):A("Failed to load food items")}catch(e){A("Failed to load food items")}finally{g(!1)}});return j?E.jsx("div",{className:"restaurant-error-page",children:E.jsxs("div",{className:"error-content",children:[E.jsxs("h2",{children:["😕 ",j]}),E.jsx("button",{onClick:()=>t("/"),className:"back-home-btn",children:"Back to Home"})]})}):r?E.jsxs("div",{className:"restaurant-page",children:[E.jsx("div",{className:"restaurant-header",children:E.jsxs("div",{className:"restaurant-banner",children:[E.jsx("img",{src:G(r.image),alt:r.name,onError:V}),E.jsx("div",{className:"restaurant-overlay",children:E.jsxs("div",{className:"restaurant-info",children:[E.jsx("h1",{className:"restaurant-title",children:r.name}),E.jsx("p",{className:"restaurant-desc",children:r.description}),E.jsxs("div",{className:"restaurant-meta",children:[E.jsxs("div",{className:"meta-item",children:[E.jsx("span",{className:"meta-icon",children:"⭐"}),E.jsx("span",{children:r.rating})]}),E.jsxs("div",{className:"meta-item",children:[E.jsx("span",{className:"meta-icon",children:"🕒"}),E.jsx("span",{children:r.deliveryTime})]}),E.jsxs("div",{className:"meta-item",children:[E.jsx("span",{className:"meta-icon",children:"🚚"}),E.jsx("span",{children:0===r.deliveryFee?"Free Delivery":`₹${r.deliveryFee}`})]}),r.minimumOrder>0&&E.jsxs("div",{className:"meta-item",children:[E.jsx("span",{className:"meta-icon",children:"💰"}),E.jsxs("span",{children:["Min ₹",r.minimumOrder]})]})]})]})})]})}),l.length>1&&E.jsx("div",{className:"category-filter",children:E.jsx("div",{className:"category-container",children:l.map(e=>E.jsx("button",{className:"category-btn "+(d===e?"active":""),onClick:()=>u(e),children:e},e))})}),E.jsx("div",{className:"restaurant-menu",children:E.jsxs("div",{className:"menu-container",children:[E.jsxs("h2",{className:"menu-title",children:["All"===d?"All Items":d,E.jsxs("span",{className:"item-count",children:["(",n.length," items)"]})]}),p?E.jsx("div",{className:"menu-loading",children:E.jsx("div",{className:"loading-grid",children:[...Array(6)].map((e,s)=>E.jsxs("div",{className:"food-item-skeleton",children:[E.jsx("div",{className:"skeleton-image"}),E.jsxs("div",{className:"skeleton-content",children:[E.jsx("div",{className:"skeleton-title"}),E.jsx("div",{className:"skeleton-description"}),E.jsx("div",{className:"skeleton-price"})]})]},s))})}):0===n.length?E.jsxs("div",{className:"no-items",children:[E.jsx("p",{children:"🍽️ No items available in this category"}),E.jsx("p",{children:"Try selecting a different category"})]}):E.jsx("div",{className:"food-items-grid",children:n.map(e=>E.jsx(Ae,{id:e._id,name:e.name,description:e.description,price:e.price,image:e.image,originalPrice:e.originalPrice,discountPercentage:e.discountPercentage,isOnSale:e.isOnSale,discountLabel:e.discountLabel,isPopular:e.isPopular,isFeatured:e.isFeatured,tags:e.tags},e._id))})]})})]}):E.jsx("div",{className:"restaurant-loading-page",children:E.jsxs("div",{className:"loading-content",children:[E.jsx("div",{className:"loading-spinner"}),E.jsx("p",{children:"Loading restaurant..."})]})})},Ue=()=>{const[s]=j(),t=s.get("success"),a=s.get("orderId"),{url:r}=m.useContext(J),i=x();return m.useEffect(()=>{h(e,null,function*(){(yield b.post(r+"/api/order/verify",{success:t,orderId:a})).data.success?i("/myorders"):i("/")})},[r,t,a,i]),E.jsx("div",{className:"verify",children:E.jsx("div",{className:"spinner"})})},Oe=()=>{const{setTokenAndUser:s,fetchUserProfile:t}=m.useContext(J),a=x(),r=A(),[i,n]=m.useState(!0);return m.useEffect(()=>{h(e,null,function*(){try{const e=new URLSearchParams(r.search),t=e.get("authError"),i=e.get("message");if("true"===t)return alert(`Authentication failed: ${i||"Unknown error"}`),void setTimeout(()=>{a("/",{replace:!0})},2e3);const n=e.get("token");if(!n)return alert("Authentication failed: No token received"),void setTimeout(()=>{a("/",{replace:!0})},2e3);const o={id:e.get("id"),name:e.get("name"),email:e.get("email"),googleId:e.get("googleId"),profileImage:e.get("picture")};s(n,o),setTimeout(()=>{a("/",{replace:!0})},1e3)}catch(e){setTimeout(()=>{a("/",{replace:!0})},1500)}finally{n(!1)}})},[r.search,a,s,t]),E.jsxs("div",{style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",height:"100vh"},children:[E.jsx("h2",{children:"Authentication successful!"}),E.jsx("p",{children:i?"Loading your profile...":"Redirecting to homepage..."})]})},Qe=()=>{const{user:e,token:s,isUserLoading:t,fetchUserProfile:a}=m.useContext(J);return E.jsxs("div",{style:{position:"fixed",top:"10px",right:"10px",background:"white",border:"1px solid #ccc",padding:"10px",borderRadius:"5px",fontSize:"12px",maxWidth:"300px",zIndex:9999,boxShadow:"0 2px 10px rgba(0,0,0,0.1)"},children:[E.jsx("h4",{children:"🔍 Auth Debug Panel"}),E.jsxs("div",{style:{marginBottom:"10px"},children:[E.jsx("strong",{children:"Token:"})," ",s?"✅ Present":"❌ Missing"]}),E.jsxs("div",{style:{marginBottom:"10px"},children:[E.jsx("strong",{children:"User Loading:"})," ",t?"⏳ Loading...":"✅ Ready"]}),E.jsxs("div",{style:{marginBottom:"10px"},children:[E.jsx("strong",{children:"User Data:"}),e?E.jsxs("div",{style:{marginLeft:"10px",fontSize:"11px"},children:[E.jsxs("div",{children:["Name: ",e.name||"N/A"]}),E.jsxs("div",{children:["Email: ",e.email||"N/A"]}),E.jsxs("div",{children:["ID: ",e.id||"N/A"]})]}):E.jsx("span",{children:" ❌ No user data"})]}),E.jsxs("div",{style:{marginBottom:"10px"},children:[E.jsx("strong",{children:"LocalStorage:"}),E.jsxs("div",{style:{marginLeft:"10px",fontSize:"11px"},children:[E.jsxs("div",{children:["Token: ",localStorage.getItem("token")?"✅":"❌"]}),E.jsxs("div",{children:["User: ",localStorage.getItem("user")?"✅":"❌"]})]})]}),E.jsxs("div",{style:{display:"flex",gap:"5px",flexWrap:"wrap"},children:[E.jsx("button",{onClick:()=>{a()},style:{fontSize:"10px",padding:"2px 5px"},children:"Fetch Profile"}),E.jsx("button",{onClick:()=>{localStorage.removeItem("user"),localStorage.removeItem("token"),window.location.reload()},style:{fontSize:"10px",padding:"2px 5px",background:"#ff4444",color:"white"},children:"Clear Storage"})]})]})},De=()=>{const[s,t]=m.useState("Testing..."),[a,r]=m.useState([]),[i,n]=m.useState([]);return m.useEffect(()=>{h(e,null,function*(){try{const e=yield fetch("https://eatzone.onrender.com/api/category/list"),s=yield e.json();s.success&&r(s.data);const a=yield fetch("https://eatzone.onrender.com/api/restaurant/list"),i=yield a.json();i.success&&n(i.data),t("✅ API Working")}catch(e){t("❌ API Error: "+e.message)}})},[]),E.jsxs("div",{style:{padding:"20px",fontFamily:"Arial, sans-serif",maxWidth:"1200px",margin:"0 auto"},children:[E.jsx("h1",{style:{color:"#ff6b35",textAlign:"center"},children:"🍕 EatZone Diagnostic"}),E.jsxs("div",{style:{backgroundColor:"#f8f9fa",padding:"20px",borderRadius:"8px",marginBottom:"20px"},children:[E.jsx("h2",{children:"🔍 System Status"}),E.jsxs("p",{children:[E.jsx("strong",{children:"API Status:"})," ",s]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Categories Count:"})," ",a.length]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Restaurants Count:"})," ",i.length]}),E.jsxs("p",{children:[E.jsx("strong",{children:"Environment:"})," ","production"]}),E.jsxs("p",{children:[E.jsx("strong",{children:"API URL:"})," ","https://eatzone.onrender.com"]})]}),a.length>0&&E.jsxs("div",{style:{marginBottom:"20px"},children:[E.jsxs("h2",{children:["🍽️ Categories (",a.length,")"]}),E.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(200px, 1fr))",gap:"15px"},children:a.slice(0,8).map(e=>E.jsxs("div",{style:{backgroundColor:"white",padding:"15px",borderRadius:"8px",border:"1px solid #dee2e6",textAlign:"center"},children:[E.jsx("div",{style:{fontSize:"24px",marginBottom:"8px"},children:e.emoji||"🍽️"}),E.jsx("h4",{style:{margin:"0 0 8px 0",color:"#333"},children:e.name}),E.jsx("p",{style:{margin:"0",fontSize:"12px",color:"#666"},children:e.description})]},e._id))})]}),i.length>0&&E.jsxs("div",{style:{marginBottom:"20px"},children:[E.jsxs("h2",{children:["🏪 Restaurants (",i.length,")"]}),E.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(250px, 1fr))",gap:"15px"},children:i.slice(0,6).map(e=>E.jsxs("div",{style:{backgroundColor:"white",padding:"15px",borderRadius:"8px",border:"1px solid #dee2e6"},children:[E.jsx("h4",{style:{margin:"0 0 8px 0",color:"#333"},children:e.name}),E.jsx("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#666"},children:e.description}),E.jsxs("p",{style:{margin:"0",fontSize:"12px",color:"#999"},children:["Delivery: ",e.deliveryTime," mins | Fee: ₹",e.deliveryFee]})]},e._id))})]}),E.jsxs("div",{style:{backgroundColor:"#e7f3ff",padding:"20px",borderRadius:"8px",textAlign:"center"},children:[E.jsx("h3",{children:"🎯 Next Steps"}),E.jsx("p",{children:"If you can see this page with data, the API is working correctly."}),E.jsx("p",{children:"The issue might be with specific components in the main app."}),E.jsx("button",{onClick:()=>window.location.href="/",style:{backgroundColor:"#ff6b35",color:"white",border:"none",padding:"10px 20px",borderRadius:"5px",cursor:"pointer",fontSize:"16px"},children:"🔄 Back to Main App"})]})]})},Me=()=>{const[e,s]=m.useState("All");return E.jsxs("div",{style:{padding:"20px",fontFamily:"Arial, sans-serif"},children:[E.jsx("h1",{style:{color:"#ff6b35",textAlign:"center"},children:"🍕 Welcome to EatZone!"}),E.jsxs("div",{style:{backgroundColor:"#f8f9fa",padding:"40px 20px",borderRadius:"8px",textAlign:"center",marginBottom:"30px"},children:[E.jsx("h2",{children:"Order your favourite food here"}),E.jsx("p",{children:"Choose from a diverse menu featuring a delicious array of dishes crafted with the finest ingredients."}),E.jsx("button",{style:{backgroundColor:"#ff6b35",color:"white",border:"none",padding:"12px 24px",borderRadius:"5px",cursor:"pointer",fontSize:"16px"},children:"View Menu"})]}),E.jsxs("div",{style:{marginBottom:"30px"},children:[E.jsx("h2",{children:"🏪 Top Restaurants"}),E.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(250px, 1fr))",gap:"20px"},children:[1,2,3,4].map(e=>E.jsxs("div",{style:{backgroundColor:"white",padding:"20px",borderRadius:"8px",border:"1px solid #dee2e6",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[E.jsx("div",{style:{backgroundColor:"#f8f9fa",height:"150px",borderRadius:"5px",display:"flex",alignItems:"center",justifyContent:"center",marginBottom:"15px",fontSize:"48px"},children:"🏪"}),E.jsxs("h3",{style:{margin:"0 0 8px 0"},children:["Restaurant ",e]}),E.jsx("p",{style:{margin:"0 0 8px 0",color:"#666"},children:"Delicious food and great service"}),E.jsx("p",{style:{margin:"0",fontSize:"14px",color:"#999"},children:"⏱️ 25-30 mins | 🚚 ₹40 delivery"})]},e))})]}),E.jsxs("div",{style:{marginBottom:"30px"},children:[E.jsx("h2",{children:"🍽️ Explore Our Menu"}),E.jsx("p",{style:{color:"#666",marginBottom:"20px"},children:"Choose from a diverse menu featuring a selection of dishes."}),E.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(120px, 1fr))",gap:"15px",marginBottom:"20px"},children:[{name:"Burgers",emoji:"🍔"},{name:"Pizza",emoji:"🍕"},{name:"Biryani",emoji:"🍛"},{name:"Salads",emoji:"🥗"},{name:"Noodles",emoji:"🍜"},{name:"Desserts",emoji:"🍦"},{name:"Beverages",emoji:"☕"},{name:"Sandwiches",emoji:"🥪"}].map(t=>E.jsxs("div",{onClick:()=>s(e=>e===t.name?"All":t.name),style:{backgroundColor:e===t.name?"#ff6b35":"white",color:e===t.name?"white":"#333",padding:"15px",borderRadius:"8px",border:"1px solid #dee2e6",textAlign:"center",cursor:"pointer",transition:"all 0.3s ease"},children:[E.jsx("div",{style:{fontSize:"24px",marginBottom:"8px"},children:t.emoji}),E.jsx("div",{style:{fontSize:"12px",fontWeight:"500"},children:t.name})]},t.name))}),E.jsx("hr",{style:{border:"none",borderTop:"1px solid #dee2e6"}})]}),E.jsxs("div",{style:{marginBottom:"30px"},children:[E.jsxs("h2",{children:["🍽️ Food Items ","All"!==e&&`- ${e}`]}),E.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(200px, 1fr))",gap:"20px"},children:[1,2,3,4,5,6].map(e=>E.jsxs("div",{style:{backgroundColor:"white",borderRadius:"8px",border:"1px solid #dee2e6",overflow:"hidden",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[E.jsx("div",{style:{backgroundColor:"#f8f9fa",height:"120px",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"36px"},children:"🍽️"}),E.jsxs("div",{style:{padding:"15px"},children:[E.jsxs("h4",{style:{margin:"0 0 8px 0"},children:["Food Item ",e]}),E.jsx("p",{style:{margin:"0 0 8px 0",fontSize:"14px",color:"#666"},children:"Delicious and fresh food item"}),E.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[E.jsxs("span",{style:{fontWeight:"bold",color:"#ff6b35"},children:["₹",150+50*e]}),E.jsx("button",{style:{backgroundColor:"#ff6b35",color:"white",border:"none",padding:"6px 12px",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},children:"Add to Cart"})]})]})]},e))})]}),E.jsxs("div",{style:{backgroundColor:"#333",color:"white",padding:"40px 20px",borderRadius:"8px",textAlign:"center"},children:[E.jsx("h3",{children:"📱 Download EatZone App"}),E.jsx("p",{children:"Get the best food delivery experience on your mobile device"}),E.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"center",marginTop:"20px"},children:[E.jsx("button",{style:{backgroundColor:"#ff6b35",color:"white",border:"none",padding:"10px 20px",borderRadius:"5px",cursor:"pointer"},children:"📱 App Store"}),E.jsx("button",{style:{backgroundColor:"#ff6b35",color:"white",border:"none",padding:"10px 20px",borderRadius:"5px",cursor:"pointer"},children:"🤖 Play Store"})]})]})]})},We=()=>{const[e,s]=m.useState(!1),{token:t}=m.useContext(J),a=({children:e})=>t?e:E.jsx(N,{to:"/",replace:!0});return E.jsxs(E.Fragment,{children:[e&&E.jsx($,{setShowLogin:s}),E.jsx(Qe,{}),E.jsxs("div",{className:"app",children:[E.jsx(q,{setShowLogin:s}),E.jsxs(v,{children:[E.jsx(y,{path:"/",element:E.jsx(Be,{})}),E.jsx(y,{path:"/cart",element:E.jsx(Te,{})}),E.jsx(y,{path:"/order",element:E.jsx(Fe,{})}),E.jsx(y,{path:"/restaurant/:id",element:E.jsx(Le,{})}),E.jsx(y,{path:"/verify",element:E.jsx(Ue,{})}),E.jsx(y,{path:"/auth/*",element:E.jsx(Oe,{})}),E.jsx(y,{path:"/myorders",element:E.jsx(a,{children:E.jsx(ze,{})})}),E.jsx(y,{path:"/profile",element:E.jsx(a,{children:E.jsx(Pe,{})})}),E.jsx(y,{path:"/diagnostic",element:E.jsx(De,{})}),E.jsx(y,{path:"/test",element:E.jsxs("div",{style:{padding:"20px"},children:[E.jsx("h1",{children:"🍕 EatZone Test Page"}),E.jsx("p",{children:"This is a simple test page to verify routing works."}),E.jsx("a",{href:"/diagnostic",children:"Go to Diagnostic"})," | ",E.jsx("a",{href:"/",children:"Go to Home"})]})}),E.jsx(y,{path:"/simple",element:E.jsx(Me,{})}),E.jsx(y,{path:"*",element:E.jsxs("div",{style:{padding:"20px",textAlign:"center"},children:[E.jsx("h2",{children:"Page Not Found"}),E.jsx("p",{children:"The page you're looking for doesn't exist."}),E.jsx("button",{onClick:()=>window.location.href="/",children:"Go Home"})]})})]}),E.jsx(Z,{})]})]})};class Je extends p.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,s){this.setState({error:e,errorInfo:s})}render(){return this.state.hasError?E.jsxs("div",{style:{padding:"20px",textAlign:"center",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"8px",margin:"20px",fontFamily:"Arial, sans-serif"},children:[E.jsx("h2",{style:{color:"#dc3545",marginBottom:"16px"},children:"🚨 Something went wrong"}),E.jsx("p",{style:{color:"#6c757d",marginBottom:"16px"},children:"We're sorry, but there was an error loading this part of the application."}),E.jsx("button",{onClick:()=>{this.setState({hasError:!1,error:null,errorInfo:null}),window.location.reload()},style:{backgroundColor:"#007bff",color:"white",border:"none",padding:"10px 20px",borderRadius:"4px",cursor:"pointer",fontSize:"14px"},children:"🔄 Reload Page"}),!1]}):this.props.children}}const Ye=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function Xe(e){navigator.serviceWorker.register(e).then(e=>{e.onupdatefound=()=>{const s=e.installing;null!=s&&(s.onstatechange=()=>{"installed"===s.state&&navigator.serviceWorker.controller&&"Notification"in window&&"granted"===Notification.permission&&new Notification("EatZone Update Available",{body:"A new version of EatZone is available. Refresh to update.",icon:"/icon-192x192.png"})})}}).catch(e=>{})}function Ge(){const e=document.getElementById("root");if(e)try{S(e).render(E.jsx(Je,{children:E.jsx(w,{children:E.jsx(Y,{children:E.jsx(We,{})})})}))}catch(s){document.body.innerHTML=`\n      <div style="padding: 20px; text-align: center; font-family: Arial;">\n        <h2 style="color: red;">EatZone Failed to Load</h2>\n        <p>Error: ${s.message}</p>\n        <button onclick="location.reload()">Reload Page</button>\n      </div>\n    `}}"loading"===document.readyState?document.addEventListener("DOMContentLoaded",Ge):Ge();try{!function(){if("serviceWorker"in navigator){!function(){h(this,null,function*(){try{const e=yield navigator.serviceWorker.getRegistrations();for(const t of e){const e=t.scope,s=window.location.origin;e.startsWith(s)||(yield t.unregister())}const s=yield caches.keys();for(const t of s)(t.includes("eatzone")||t.includes("vite"))&&(yield caches.delete(t))}catch(e){}})}();const s="";try{new URL(s||"",window.location.href).origin,window.location.origin}catch(e){}window.addEventListener("load",()=>{const e="/sw.js";Ye?(function(e){fetch(e,{headers:{"Service-Worker":"script"}}).then(s=>{const t=s.headers.get("content-type");404===s.status||null!=t&&-1===t.indexOf("javascript")?navigator.serviceWorker.ready.then(e=>{e.unregister().then(()=>{window.location.reload()})}):Xe(e)}).catch(()=>{})}(e),navigator.serviceWorker.ready.then(()=>{})):Xe(e)})}}(),"Notification"in window&&"default"===Notification.permission&&Notification.requestPermission().then(e=>{})}catch(He){}}},function(){return k||(0,C[a(C)[0]])((k={exports:{}}).exports,k),k.exports});export default S();
